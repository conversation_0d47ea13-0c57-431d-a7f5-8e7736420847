# Unit Testing Setup for OpenWebUI RAG Code Server

## Overview

A comprehensive unit testing framework has been added to the project to test the code analysis server running on home-ai-server.local:5002. The testing framework uses pytest and is fully integrated with Visual Studio Code.

## Directory Structure

```
unit-tests/
├── __init__.py                    # Package initialization
├── conftest.py                    # Pytest configuration and fixtures
├── test_basic_connectivity.py     # Basic connectivity tests
├── test_server_health.py          # Server health endpoint tests
├── test_codebase_management.py    # Codebase management tests
├── test_search_functionality.py   # Search and query tests
├── test_framework_integration.py  # Framework integration tests
├── test_intent_detection.py       # Intent detection tests
├── test_analysis_endpoints.py     # Analysis service tests
├── run_tests.py                   # Test runner script
└── README.md                      # Detailed testing documentation
```

## Key Features

### 1. **Visual Studio Code Integration**
- Pytest is configured as the default test framework
- Test discovery is automatic
- Debug configurations are available
- Tests can be run individually or in groups

### 2. **Comprehensive Test Coverage**
- **Health Tests**: Server connectivity, health endpoints, status checks
- **Codebase Management**: List, select, session management, statistics
- **Search Functionality**: Enhanced search, context retrieval, legacy compatibility
- **Framework Integration**: GPU infrastructure, framework status, query enhancement
- **Intent Detection**: Query classification, configuration management
- **Analysis Endpoints**: Analysis service functionality

### 3. **Flexible Test Execution**
- Run all tests or specific test files
- Filter tests by markers (health, search, framework, etc.)
- Verbose output and coverage reporting
- Cross-platform support (Windows/Linux/Mac)

## Running Tests

### From Visual Studio Code
1. **Test Explorer**: Use the built-in test explorer panel
2. **Debug Menu**: Select "Run Unit Tests" from the debug configurations
3. **Command Palette**: Use `Python: Run All Tests`

### From Command Line
```bash
# Run all tests with verbose output
python unit-tests/run_tests.py --verbose

# Run specific test file
python unit-tests/run_tests.py --pattern test_server_health.py

# Run tests with specific markers
python unit-tests/run_tests.py --markers "health"

# Run with coverage reporting
python unit-tests/run_tests.py --coverage
```

### Quick Start Scripts
- **Windows**: `run_unit_tests.bat`
- **Linux/Mac**: `run_unit_tests.sh`

## Prerequisites

1. **Server Running**: The code analysis server must be running on home-ai-server.local:5002
2. **Dependencies**: All required packages are listed in `requirements.txt`
3. **Python Environment**: Python 3.7+ with pytest and httpx

## Test Configuration

### Server Settings
- Base URL: `http://home-ai-server.local:5002`
- Timeout: 30 seconds
- Sample codebase: `utils`

### Test Markers
- `health` - Health check tests
- `search` - Search functionality tests
- `framework` - Framework integration tests
- `codebase` - Codebase management tests
- `intent` - Intent detection tests
- `analysis` - Analysis endpoints tests

## Key Test Files

### `test_basic_connectivity.py`
Simple connectivity tests that verify the server is running and accessible. Run this first to ensure basic functionality.

### `test_server_health.py`
Comprehensive health endpoint testing including:
- Root endpoint validation
- Health status checks
- Detailed health information
- Enhanced features documentation

### `test_codebase_management.py`
Tests for codebase operations:
- Listing available codebases
- Selecting and managing codebases
- Session management
- Statistics retrieval

### `test_search_functionality.py`
Search and query testing:
- Enhanced search capabilities
- Optimized context retrieval
- Legacy endpoint compatibility
- Stateless operations

## Benefits

1. **Quality Assurance**: Automated testing ensures server functionality works as expected
2. **Regression Prevention**: Tests catch issues when making changes to the server
3. **Documentation**: Tests serve as living documentation of API behavior
4. **Development Workflow**: Integrated with VS Code for seamless development
5. **CI/CD Ready**: Tests can be integrated into continuous integration pipelines

## Next Steps

1. **Start the Server**: Ensure the code analysis server is running on home-ai-server.local:5002
2. **Install Dependencies**: Run `pip install -r requirements.txt`
3. **Run Basic Test**: Execute `python unit-tests/test_basic_connectivity.py`
4. **Full Test Suite**: Run all tests using the provided scripts or VS Code integration

## Troubleshooting

- **Connection Errors**: Verify server is running on home-ai-server.local:5002
- **Import Errors**: Ensure all dependencies are installed
- **Test Failures**: Check server logs and verify codebase availability
- **VS Code Issues**: Reload the window to refresh test discovery

The unit testing framework provides a solid foundation for ensuring the reliability and functionality of the code analysis server.
