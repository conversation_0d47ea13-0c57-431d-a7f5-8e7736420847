<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Analyzer Management Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: left;
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
            border-radius: 10px;
            color: var(--text-inverse);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: var(--text-inverse);
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            color: var(--text-inverse);
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        /* Individual Codebases Table Section */
        .codebases-table-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 24px;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* GPU Infrastructure Details Section */
        .gpu-details-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 24px;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-header {
            margin-bottom: 20px;
            text-align: center;
        }

        .section-header h2 {
            color: var(--text-primary);
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .section-header p {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
            font-style: italic;
        }
        
        .card {
            background: var(--bg-secondary);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--primary-500);
            border: 1px solid var(--border-light);
            color: var(--text-primary);
        }

        .card h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: var(--success-500); }
        .status-offline { background-color: var(--error-500); }
        .status-warning { background-color: var(--warning-500); }

        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-light);
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .metric-value {
            color: var(--text-primary);
            font-weight: bold;
        }

        /* Enhanced metrics styling */
        .metric-enhanced {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-light);
            transition: background-color 0.2s ease;
        }

        .metric-enhanced:hover {
            background-color: var(--bg-tertiary);
            border-radius: 4px;
            padding: 10px 8px;
        }

        .metric-enhanced:last-child {
            border-bottom: none;
        }

        .metric-label-enhanced {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .metric-value-enhanced {
            color: var(--text-primary);
            font-weight: bold;
            font-size: 14px;
        }

        .metric-highlight {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: var(--text-inverse);
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px 0;
        }

        .metric-highlight .metric-label-enhanced {
            color: var(--text-inverse);
            opacity: 0.9;
        }

        .metric-highlight .metric-value-enhanced {
            color: var(--text-inverse);
        }

        /* Codebase Table Styles */
        .codebase-table {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
        }

        .codebase-table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1.2fr 1fr 1fr 1fr 2fr;
            gap: 1px;
            background: var(--primary-500);
            color: var(--text-inverse);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .codebase-table-header > div {
            padding: 12px 8px;
            background: var(--primary-500);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .codebase-table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1.2fr 1fr 1fr 1fr 2fr;
            gap: 1px;
            background: var(--border-light);
            transition: background-color 0.2s ease;
        }

        .codebase-table-row:hover {
            background: var(--border-medium);
        }

        .codebase-table-row > div {
            padding: 10px 8px;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--text-primary);
            transition: background-color 0.2s ease;
        }

        .codebase-table-row:hover > div {
            background: var(--bg-tertiary);
        }

        .codebase-col-name {
            justify-content: flex-start !important;
            font-weight: 500;
        }

        .codebase-col-files,
        .codebase-col-lines,
        .codebase-col-size,
        .codebase-col-avg,
        .codebase-col-chunks {
            justify-content: center !important;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .codebase-col-languages {
            justify-content: flex-start !important;
            font-size: 11px;
            color: var(--text-secondary);
        }

        /* Responsive design for smaller screens */
        @media (max-width: 1024px) {
            .codebase-table-header,
            .codebase-table-row {
                grid-template-columns: 1.5fr 0.8fr 1fr 0.8fr 0.8fr 0.8fr 1.5fr;
            }

            .codebase-table-header > div,
            .codebase-table-row > div {
                padding: 8px 4px;
                font-size: 11px;
            }
        }

        @media (max-width: 768px) {
            .codebase-table-header,
            .codebase-table-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .codebase-table-row {
                margin-bottom: 10px;
                border-radius: 6px;
                overflow: hidden;
            }

            .codebase-table-row > div {
                justify-content: space-between !important;
                padding: 8px 12px;
                border-bottom: 1px solid var(--border-light);
            }

            .codebase-table-row > div:last-child {
                border-bottom: none;
            }

            .codebase-table-row > div::before {
                content: attr(data-label);
                font-weight: 600;
                color: var(--text-secondary);
                font-size: 11px;
                text-transform: uppercase;
            }

            .codebase-table-header {
                display: none;
            }
        }

        /* GPU Details Table Styles */
        .gpu-table {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
        }

        .gpu-table-header {
            display: grid;
            grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1fr 1.5fr;
            gap: 1px;
            background: var(--primary-500);
            color: var(--text-inverse);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .gpu-table-header > div {
            padding: 12px 8px;
            background: var(--primary-500);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .gpu-table-row {
            display: grid;
            grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1fr 1.5fr;
            gap: 1px;
            background: var(--border-light);
            transition: background-color 0.2s ease;
        }

        .gpu-table-row:hover {
            background: var(--border-medium);
        }

        .gpu-table-row > div {
            padding: 10px 8px;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--text-primary);
            transition: background-color 0.2s ease;
        }

        .gpu-table-row:hover > div {
            background: var(--bg-tertiary);
        }

        .gpu-col-host {
            justify-content: flex-start !important;
            font-weight: 500;
            font-size: 11px;
        }

        .gpu-col-type,
        .gpu-col-tier,
        .gpu-col-vram,
        .gpu-col-speed {
            justify-content: center !important;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .gpu-col-architecture {
            justify-content: flex-start !important;
            font-size: 11px;
        }

        .gpu-col-recommendations {
            justify-content: flex-start !important;
            font-size: 11px;
            color: var(--text-secondary);
        }

        /* Responsive GPU table */
        @media (max-width: 1024px) {
            .gpu-table-header,
            .gpu-table-row {
                grid-template-columns: 1.5fr 1fr 0.8fr 0.8fr 0.8fr 0.8fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .gpu-table-header,
            .gpu-table-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .gpu-table-row {
                margin-bottom: 10px;
                border-radius: 6px;
                overflow: hidden;
            }

            .gpu-table-row > div {
                justify-content: space-between !important;
                padding: 8px 12px;
                border-bottom: 1px solid var(--border-light);
            }

            .gpu-table-row > div:last-child {
                border-bottom: none;
            }

            .gpu-table-row > div::before {
                content: attr(data-label);
                font-weight: 600;
                color: var(--text-secondary);
                font-size: 11px;
                text-transform: uppercase;
            }

            .gpu-table-header {
                display: none;
            }
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .controls {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .controls h3 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #f44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: #4CAF50;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .log-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        /* Code Results Tile Layout */
        .code-results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .code-result-tile {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .code-result-tile:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
            border-color: var(--border-medium);
        }

        .code-result-header {
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
            color: var(--text-inverse);
            padding: 12px 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .code-result-title {
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .code-result-badges {
            display: flex;
            gap: 6px;
        }

        .code-badge {
            background: rgba(255,255,255,0.2);
            color: var(--text-inverse);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .code-result-content {
            padding: 16px;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .code-result-preview {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-light);
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .code-result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-secondary);
            border-top: 1px solid var(--border-light);
            padding-top: 12px;
        }

        .code-result-stats {
            display: flex;
            gap: 16px;
            color: var(--text-secondary);
        }

        .code-result-actions {
            display: flex;
            gap: 8px;
        }

        .code-action-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-medium);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-primary);
        }

        .code-action-btn:hover {
            background: var(--button-primary-bg);
            border-color: var(--button-primary-bg);
            color: var(--button-primary-text);
        }

        /* Language-specific color coding - adapted for themes */
        .lang-c_cpp .code-result-header {
            background: linear-gradient(135deg, #00599C 0%, #004482 100%);
            color: var(--text-inverse);
        }
        .lang-python .code-result-header {
            background: linear-gradient(135deg, #3776ab 0%, #306998 100%);
            color: var(--text-inverse);
        }
        .lang-javascript .code-result-header {
            background: linear-gradient(135deg, #f0db4f 0%, #323330 100%);
            color: #323330;
        }
        .lang-csharp .code-result-header {
            background: linear-gradient(135deg, #239120 0%, #68217a 100%);
            color: var(--text-inverse);
        }
        .lang-java .code-result-header {
            background: linear-gradient(135deg, #ed8b00 0%, #5382a1 100%);
            color: var(--text-inverse);
        }
        .lang-typescript .code-result-header {
            background: linear-gradient(135deg, #3178c6 0%, #235a97 100%);
            color: var(--text-inverse);
        }
        .lang-rust .code-result-header {
            background: linear-gradient(135deg, #ce422b 0%, #8b2500 100%);
            color: var(--text-inverse);
        }
        .lang-go .code-result-header {
            background: linear-gradient(135deg, #00add8 0%, #007d9c 100%);
            color: var(--text-inverse);
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .code-results-container {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .code-results-container {
                grid-template-columns: 1fr;
                gap: 16px;
                margin: 16px 10px;
            }

            .code-result-tile {
                margin: 0;
            }

            .code-result-header {
                padding: 10px 12px;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .code-result-title {
                font-size: 13px;
            }

            .code-result-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .code-result-stats {
                gap: 12px;
            }
        }

        /* ==============================================
           ACCESSIBLE WEB APP COLOR THEMES
           Based on WCAG 2.1 AA/AAA contrast requirements
           ============================================== */

        /* LIGHT THEME (Default) */
        :root[data-theme="light"] {
          /* Primary Colors */
          --primary-500: #0ea5e9;
          --primary-600: #0284c7;
          --primary-700: #0369a1;

          /* Neutral Colors */
          --neutral-50: #f8fafc;
          --neutral-100: #f1f5f9;
          --neutral-200: #e2e8f0;
          --neutral-300: #cbd5e1;
          --neutral-600: #475569;
          --neutral-700: #334155;
          --neutral-900: #0f172a;

          /* Semantic Colors */
          --success-500: #22c55e;
          --warning-500: #f59e0b;
          --error-500: #ef4444;

          /* Background Colors */
          --bg-primary: #ffffff;
          --bg-secondary: var(--neutral-50);
          --bg-tertiary: var(--neutral-100);

          /* Text Colors - All meet WCAG AA contrast (4.5:1+) */
          --text-primary: var(--neutral-900);
          --text-secondary: var(--neutral-700);
          --text-tertiary: var(--neutral-600);
          --text-inverse: #ffffff;

          /* Border Colors */
          --border-light: var(--neutral-200);
          --border-medium: var(--neutral-300);

          /* Interactive Colors */
          --link-primary: var(--primary-600);
          --link-hover: var(--primary-700);
          --button-primary-bg: var(--primary-600);
          --button-primary-text: #ffffff;
        }

        /* DARK THEME */
        :root[data-theme="dark"] {
          /* Primary Colors - Adjusted for dark backgrounds */
          --primary-400: #0ea5e9;
          --primary-500: #38bdf8;
          --primary-600: #7dd3fc;

          /* Neutral Colors */
          --neutral-50: #0f172a;
          --neutral-100: #1e293b;
          --neutral-200: #334155;
          --neutral-300: #475569;
          --neutral-600: #cbd5e1;
          --neutral-700: #e2e8f0;
          --neutral-900: #f8fafc;

          /* Semantic Colors - Adjusted for dark theme */
          --success-500: #4ade80;
          --warning-500: #fbbf24;
          --error-500: #f87171;

          /* Background Colors - Using dark gray instead of pure black */
          --bg-primary: #0f172a;
          --bg-secondary: #1e293b;
          --bg-tertiary: #334155;

          /* Text Colors - All meet WCAG AA contrast (4.5:1+) on dark backgrounds */
          --text-primary: var(--neutral-900);
          --text-secondary: var(--neutral-700);
          --text-tertiary: var(--neutral-600);
          --text-inverse: var(--neutral-100);

          /* Border Colors */
          --border-light: var(--neutral-200);
          --border-medium: var(--neutral-300);

          /* Interactive Colors */
          --link-primary: var(--primary-400);
          --link-hover: var(--primary-500);
          --button-primary-bg: var(--primary-500);
          --button-primary-text: var(--neutral-900);
        }

        /* BLUE CORPORATE THEME */
        :root[data-theme="blue"] {
          /* Blue Color Scale */
          --primary-500: #3b82f6;
          --primary-600: #2563eb;
          --primary-700: #1d4ed8;

          /* Neutral Colors */
          --neutral-50: #f8fafc;
          --neutral-100: #f1f5f9;
          --neutral-200: #e2e8f0;
          --neutral-300: #cbd5e1;
          --neutral-600: #475569;
          --neutral-700: #334155;
          --neutral-900: #0f172a;

          /* Semantic Colors */
          --success-500: #10b981;
          --warning-500: #f59e0b;
          --error-500: #ef4444;

          /* Background Colors */
          --bg-primary: #ffffff;
          --bg-secondary: #eff6ff;
          --bg-tertiary: var(--neutral-50);

          /* Text Colors */
          --text-primary: var(--neutral-900);
          --text-secondary: var(--neutral-700);
          --text-tertiary: var(--neutral-600);
          --text-inverse: #ffffff;

          /* Border Colors */
          --border-light: #bfdbfe;
          --border-medium: #93c5fd;

          /* Interactive Colors */
          --link-primary: var(--primary-600);
          --link-hover: var(--primary-700);
          --button-primary-bg: var(--primary-600);
          --button-primary-text: #ffffff;
        }

        /* GREEN NATURE THEME */
        :root[data-theme="green"] {
          /* Green Color Scale */
          --primary-500: #22c55e;
          --primary-600: #16a34a;
          --primary-700: #15803d;

          /* Neutral Colors */
          --neutral-50: #f9fafb;
          --neutral-100: #f3f4f6;
          --neutral-200: #e5e7eb;
          --neutral-300: #d1d5db;
          --neutral-600: #4b5563;
          --neutral-700: #374151;
          --neutral-900: #111827;

          /* Semantic Colors */
          --success-500: var(--primary-500);
          --warning-500: #f59e0b;
          --error-500: #ef4444;

          /* Background Colors */
          --bg-primary: #ffffff;
          --bg-secondary: #f0fdf4;
          --bg-tertiary: var(--neutral-50);

          /* Text Colors */
          --text-primary: var(--neutral-900);
          --text-secondary: var(--neutral-700);
          --text-tertiary: var(--neutral-600);
          --text-inverse: #ffffff;

          /* Border Colors */
          --border-light: #bbf7d0;
          --border-medium: #86efac;

          /* Interactive Colors */
          --link-primary: var(--primary-600);
          --link-hover: var(--primary-700);
          --button-primary-bg: var(--primary-600);
          --button-primary-text: #ffffff;
        }

        /* PURPLE CREATIVE THEME */
        :root[data-theme="purple"] {
          /* Purple Color Scale */
          --primary-500: #a855f7;
          --primary-600: #9333ea;
          --primary-700: #7c3aed;

          /* Neutral Colors */
          --neutral-50: #fafafa;
          --neutral-100: #f4f4f5;
          --neutral-200: #e4e4e7;
          --neutral-300: #d4d4d8;
          --neutral-600: #52525b;
          --neutral-700: #3f3f46;
          --neutral-900: #18181b;

          /* Semantic Colors */
          --success-500: #22c55e;
          --warning-500: #f59e0b;
          --error-500: #ef4444;

          /* Background Colors */
          --bg-primary: #ffffff;
          --bg-secondary: #faf5ff;
          --bg-tertiary: var(--neutral-50);

          /* Text Colors */
          --text-primary: var(--neutral-900);
          --text-secondary: var(--neutral-700);
          --text-tertiary: var(--neutral-600);
          --text-inverse: #ffffff;

          /* Border Colors */
          --border-light: #e9d5ff;
          --border-medium: #d8b4fe;

          /* Interactive Colors */
          --link-primary: var(--primary-600);
          --link-hover: var(--primary-700);
          --button-primary-bg: var(--primary-600);
          --button-primary-text: #ffffff;
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .container {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            color: var(--text-primary);
        }

        .btn {
            border: 1px solid var(--border-medium);
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: var(--button-primary-bg);
            color: var(--button-primary-text);
            border-color: var(--button-primary-bg);
            transform: translateY(-1px);
        }

        .controls {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            color: var(--text-primary);
        }

        .controls h3 {
            color: var(--text-primary);
        }

        .control-group label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .control-group input, .control-group select {
            background: var(--bg-primary);
            border: 1px solid var(--border-medium);
            color: var(--text-primary);
        }

        .control-group input:focus, .control-group select:focus {
            border-color: var(--link-primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        #progress-container {
            background: var(--bg-tertiary);
            border-left: 4px solid var(--link-primary);
            color: var(--text-primary);
        }

        #control-output {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            color: var(--text-primary);
        }

        /* Status indicators with proper contrast */
        .success {
            background-color: var(--bg-secondary);
            color: var(--success-500);
            border: 1px solid var(--success-500);
            padding: 10px;
            border-radius: 5px;
        }

        .error {
            background-color: var(--bg-secondary);
            color: var(--error-500);
            border: 1px solid var(--error-500);
            padding: 10px;
            border-radius: 5px;
        }

        .loading {
            background-color: var(--bg-secondary);
            color: var(--warning-500);
            border: 1px solid var(--warning-500);
            padding: 10px;
            border-radius: 5px;
        }

        /* Links with proper contrast */
        a {
            color: var(--link-primary);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover);
            text-decoration: underline;
        }

        /* Hybrid Analysis Styles */
        .hybrid-model-selection {
            margin: 20px 0;
            padding: 15px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-light);
        }

        .model-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .model-group {
            padding: 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 6px;
        }

        .model-group h5 {
            margin: 0 0 8px 0;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .model-option {
            margin: 6px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .model-option input[type="radio"] {
            margin: 0;
        }

        .model-option label {
            font-size: 13px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .hybrid-query-interface {
            margin: 20px 0;
        }

        .query-controls {
            margin-top: 10px;
        }

        #hybrid-query {
            width: 100%;
            height: 120px;
            padding: 12px;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            resize: vertical;
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .query-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            padding: 10px 0;
        }

        .query-options label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: var(--text-secondary);
        }

        .hybrid-results {
            margin: 20px 0;
            padding: 15px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-light);
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .metric-card {
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 6px;
            text-align: center;
            border: 1px solid var(--border-light);
        }

        .metric-card .metric-label {
            font-size: 11px;
            color: var(--text-secondary);
            text-transform: uppercase;
            margin-bottom: 4px;
        }

        .metric-card .metric-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .analysis-content {
            background: var(--bg-secondary);
            border: 1px solid var(--border-light);
            border-radius: 6px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            color: var(--text-primary);
        }

        .status-indicator {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 13px;
            margin: 10px 0;
        }

        .status-indicator.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-indicator.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <h1>🔧 Code Analyzer Management Dashboard</h1>
                    <p>Monitor and control your Code Analyzer Server at port 5002</p>
                </div>
                <div class="theme-selector">
                    <label for="theme-select" style="font-size: 14px; margin-right: 10px; color: var(--text-secondary);">Theme:</label>
                    <select id="theme-select" onchange="changeTheme()" style="padding: 8px 12px; border-radius: 6px; border: 1px solid var(--border-medium); background: var(--bg-primary); color: var(--text-primary); font-size: 14px;">
                        <option value="light">🌞 Light (Default)</option>
                        <option value="dark">🌙 Dark (High Contrast)</option>
                        <option value="blue">🔵 Blue (Corporate)</option>
                        <option value="green">🟢 Green (Nature)</option>
                        <option value="purple">🟣 Purple (Creative)</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <!-- Server Health Card -->
            <div class="card">
                <h3>🏥 Server Health</h3>
                <div id="health-status" class="loading">Loading health status...</div>
            </div>
            
            <!-- Codebases Card -->
            <div class="card">
                <h3>📚 Codebases</h3>
                <div id="codebases-status" class="loading">Loading codebases...</div>
            </div>
            
            <!-- GPU Status Card -->
            <div class="card">
                <h3>⚡ GPU Infrastructure</h3>
                <div id="gpu-status" class="loading">Loading GPU status...</div>
            </div>
            
            <!-- Performance Metrics Card -->
            <div class="card">
                <h3>📊 Performance Metrics</h3>
                <div id="metrics-status" class="loading">Loading metrics...</div>
            </div>
        </div>

        <!-- Individual Codebases Table Section -->
        <div class="codebases-table-section">
            <div class="section-header">
                <h2>📁 Individual Codebases</h2>
                <p>Detailed breakdown of each codebase with source code and vector database statistics</p>
            </div>
            <div id="individual-codebases-table" class="loading">Loading individual codebase details...</div>
        </div>

        <!-- GPU Infrastructure Details Section -->
        <div class="gpu-details-section">
            <div class="section-header">
                <h2>🎮 GPU Infrastructure Details</h2>
                <p>Comprehensive GPU specifications, performance metrics, and processing capabilities</p>
            </div>
            <div id="gpu-details-table" class="loading">Loading GPU infrastructure details...</div>
        </div>

        <!-- Hybrid Code Analysis Section -->
        <div class="gpu-details-section">
            <div class="section-header">
                <h2>🚀 Hybrid Code Analysis</h2>
                <p>Intelligent routing between local OpenWebUI and remote Ollama instances for optimal performance</p>
            </div>
            <div id="hybrid-analysis-section">

                <!-- Model Selection -->
                <div class="hybrid-model-selection">
                    <h4>📋 Available Models</h4>
                    <div class="model-grid" id="model-grid">
                        <div class="loading">Loading available models...</div>
                    </div>
                </div>

                <!-- Query Interface -->
                <div class="hybrid-query-interface">
                    <h4>🔍 Code Analysis Query</h4>
                    <div class="query-controls">
                        <textarea id="hybrid-query" placeholder="Enter your code analysis query...
Examples:
• List all available codebases
• Find memory management functions in utils codebase
• What programming languages are used in z80emu?
• Get statistics for TypeScript-Node-Starter-master"></textarea>
                        <div class="query-options">
                            <label>
                                <input type="checkbox" id="prefer-fast"> Prefer fastest model
                            </label>
                            <button class="btn primary" onclick="runHybridAnalysis()">🚀 Analyze Code</button>
                        </div>
                    </div>
                </div>

                <!-- Results Display -->
                <div class="hybrid-results" id="hybrid-results" style="display: none;">
                    <h4>📊 Analysis Results</h4>
                    <div class="performance-metrics" id="performance-metrics"></div>
                    <div class="analysis-content" id="analysis-content"></div>
                </div>

            </div>
        </div>

        <!-- Controls Section -->
        <div class="controls">
            <h3>🎛️ Server Controls</h3>
            
            <div class="control-group">
                <label>Select Active Codebase:</label>
                <select id="codebase-select" onchange="updateVectorDBButtons()">
                    <option value="">Loading codebases...</option>
                </select>
                <button class="btn" onclick="selectCodebase()">Select Codebase</button>
            </div>

            <div class="control-group">
                <label>🧪 Test Questions (Debug/Testing):</label>
                <select id="test-questions-select" onchange="loadTestQuestion()" style="margin-bottom: 10px;">
                    <option value="">Select a codebase above first...</option>
                </select>
                <small style="color: #666; font-size: 12px; display: block; margin-bottom: 10px;">
                    Questions will appear after selecting a codebase
                </small>
            </div>

            <div class="control-group">
                <label>Test Query:</label>
                <input type="text" id="test-query" placeholder="Enter a test query..." value="Find memory management functions">
                <div style="display: flex; gap: 10px; align-items: center; margin: 5px 0;">
                    <label for="results-count" style="font-weight: bold;">Results:</label>
                    <select id="results-count" style="padding: 5px;">
                        <option value="5">5 results</option>
                        <option value="10">10 results</option>
                        <option value="15" selected>15 results (default)</option>
                        <option value="20">20 results</option>
                        <option value="25">25 results</option>
                        <option value="30">30 results</option>
                    </select>
                    <span style="font-size: 12px; color: #666;">More results = better analysis</span>
                </div>
                <button class="btn" onclick="testQuery()">Run Test Query</button>
            </div>
            
            <div class="control-group">
                <button class="btn" onclick="refreshAll()">🔄 Refresh All Data</button>
                <button class="btn" onclick="refreshGPUs()">🔄 Refresh GPUs</button>
                <button class="btn" onclick="restartServer()" disabled>🔄 Restart Server</button>
            </div>

            <div class="control-group">
                <h4>🗄️ Vector Database Management</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                    <button id="create-btn" class="btn" onclick="createVectorDB()" style="background: linear-gradient(135deg, #27ae60, #2ecc71);" disabled>➕ Create New</button>
                    <button id="rebuild-btn" class="btn" onclick="rebuildVectorDB()" style="background: linear-gradient(135deg, #f39c12, #e67e22);" disabled>🔄 Rebuild</button>
                    <button id="reprocess-btn" class="btn" onclick="reprocessVectorDB()" style="background: linear-gradient(135deg, #3498db, #2980b9);" disabled>⚙️ Reprocess</button>
                    <button id="delete-btn" class="btn" onclick="deleteVectorDB()" style="background: linear-gradient(135deg, #e74c3c, #c0392b);" disabled>🗑️ Delete</button>
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    <strong>Create:</strong> New codebase | <strong>Rebuild:</strong> Delete + recreate | <strong>Reprocess:</strong> Update existing | <strong>Delete:</strong> Remove completely
                </div>
            </div>

            <div class="control-group">
                <h4>🔄 Bulk Operations</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 10px 0;">
                    <button class="btn" onclick="reprocessAllCodebases()" style="background: linear-gradient(135deg, #17a2b8, #138496);">🔄 Reprocess All Embeddings</button>
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    ⚠️ <strong>Warning:</strong> Bulk operations affect ALL codebases and may take a very long time (several minutes per codebase)
                </div>
            </div>

            <div id="progress-container" style="display: none; margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 id="progress-title">Operation Progress</h4>
                <div id="progress-bar-container" style="background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; overflow: hidden;">
                    <div id="progress-bar" style="background: linear-gradient(90deg, #007bff, #0056b3); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                </div>
                <div id="progress-text" style="font-size: 14px; color: #666;">Initializing...</div>
                <div id="progress-details" style="font-size: 12px; color: #888; margin-top: 10px; max-height: 200px; overflow-y: auto;"></div>
            </div>

            <div id="control-output"></div>
        </div>
    </div>

    <script>
        // Auto-refresh interval (30 seconds)
        let refreshInterval;
        
        // Progress tracking WebSocket
        let progressWebSocket = null;

        function initializeProgressWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/progress`;

            progressWebSocket = new WebSocket(wsUrl);

            progressWebSocket.onopen = function() {
                console.log('Progress WebSocket connected');
            };

            progressWebSocket.onmessage = function(event) {
                const progressData = JSON.parse(event.data);
                updateProgressDisplay(progressData);
            };

            progressWebSocket.onclose = function() {
                console.log('Progress WebSocket disconnected');
                // Reconnect after 3 seconds
                setTimeout(initializeProgressWebSocket, 3000);
            };

            progressWebSocket.onerror = function(error) {
                console.error('Progress WebSocket error:', error);
            };
        }

        function updateProgressDisplay(progressData) {
            const progressContainer = document.getElementById('progress-container');
            const progressTitle = document.getElementById('progress-title');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const progressDetails = document.getElementById('progress-details');

            if (progressData.status === 'running') {
                progressContainer.style.display = 'block';
                progressTitle.textContent = progressData.operation_name;

                const percent = Math.round(progressData.progress_percent);
                progressBar.style.width = `${percent}%`;

                progressText.textContent = `${progressData.completed_items}/${progressData.total_items} completed (${percent}%) - ${progressData.current_item}`;

                // Update details with recent results
                if (progressData.results && progressData.results.length > 0) {
                    const recentResults = progressData.results.slice(-5); // Show last 5 results
                    progressDetails.innerHTML = recentResults.map(result => {
                        const status = result.status === 'success' ? '✅' : '❌';
                        const error = result.error ? ` - ${result.error}` : '';
                        return `<div>${status} ${result.codebase}${error}</div>`;
                    }).join('');
                }
            } else if (progressData.status === 'completed') {
                progressBar.style.width = '100%';
                progressText.textContent = `✅ ${progressData.operation_name} completed!`;

                // Hide progress after 10 seconds
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 10000);
            }
        }

        // Theme Management
        function loadTheme() {
            const savedTheme = localStorage.getItem('dashboard-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            document.getElementById('theme-select').value = savedTheme;
        }

        function changeTheme() {
            const themeSelect = document.getElementById('theme-select');
            const selectedTheme = themeSelect.value;

            document.documentElement.setAttribute('data-theme', selectedTheme);
            localStorage.setItem('dashboard-theme', selectedTheme);

            // Show theme change notification
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--accent-color, #007bff);
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            `;
            toast.innerHTML = `🎨 Theme changed to ${selectedTheme}`;
            document.body.appendChild(toast);

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            refreshAll();
            // Don't load test questions initially - wait for codebase selection
            initializeProgressWebSocket();
            startAutoRefresh();
            // Initialize vector DB buttons state
            updateVectorDBButtons();
        });
        
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshAll, 30000); // 30 seconds
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        async function refreshAll() {
            await Promise.all([
                loadHealthStatus(),
                loadCodebasesStatus(),
                loadGpuStatus(),
                loadMetrics()
            ]);
        }
        
        async function loadHealthStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                let html = '';
                if (data.server_responsive) {
                    // Server status
                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced"><span class="status-indicator status-online"></span>🏥 Server Status</span>
                        <span class="metric-value-enhanced">Online</span>
                    </div>`;

                    // Version and response time
                    if (data.basic_health && data.basic_health.version) {
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">📦 Version</span>
                            <span class="metric-value-enhanced">${data.basic_health.version}</span>
                        </div>`;
                    }

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">⚡ Response Time</span>
                        <span class="metric-value-enhanced">${(data.response_time * 1000).toFixed(0)}ms</span>
                    </div>`;

                    // System resources
                    if (data.system_resources && data.system_resources.available) {
                        const resources = data.system_resources;

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">💾 Memory Usage</span>
                            <span class="metric-value-enhanced">${resources.memory.usage_percent}%</span>
                        </div>`;

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🖥️ CPU Usage</span>
                            <span class="metric-value-enhanced">${resources.cpu.usage_percent}%</span>
                        </div>`;

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">💽 Disk Usage</span>
                            <span class="metric-value-enhanced">${resources.disk.usage_percent}%</span>
                        </div>`;
                    }

                    // System validation
                    if (data.detailed_health && data.detailed_health.system_validation) {
                        const valid = data.detailed_health.system_validation.overall_valid;
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">✅ System Valid</span>
                            <span class="metric-value-enhanced">${valid ? '✅ Yes' : '❌ No'}</span>
                        </div>`;
                    }

                    // Analysis service status
                    if (data.analysis_health && data.analysis_health.status) {
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🔍 Analysis Service</span>
                            <span class="metric-value-enhanced">${data.analysis_health.status === 'healthy' ? '✅ Healthy' : '❌ Unhealthy'}</span>
                        </div>`;
                    }

                } else {
                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced"><span class="status-indicator status-offline"></span>🏥 Server Status</span>
                        <span class="metric-value-enhanced">Offline</span>
                    </div>`;
                    html += `<div class="error">Error: ${data.error || 'Server not responding'}</div>`;
                }

                document.getElementById('health-status').innerHTML = html;
            } catch (error) {
                document.getElementById('health-status').innerHTML = `<div class="error">Failed to load health status: ${error.message}</div>`;
            }
        }
        
        async function loadCodebasesStatus() {
            try {
                // Load detailed codebase statistics
                const response = await fetch('/api/codebases/detailed');
                const data = await response.json();

                let html = '';
                if (data.success) {
                    const summary = data.summary;

                    // Summary statistics with enhanced styling
                    html += `<div class="metric-enhanced metric-highlight">
                        <span class="metric-label-enhanced">📁 Total Codebases</span>
                        <span class="metric-value-enhanced">${summary.total_codebases}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">📄 Total Files</span>
                        <span class="metric-value-enhanced">${summary.total_files.toLocaleString()}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">📏 Total Lines</span>
                        <span class="metric-value-enhanced">${summary.total_lines.toLocaleString()}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">💾 Total Size</span>
                        <span class="metric-value-enhanced">${summary.total_size_formatted}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">📊 Avg Files/Codebase</span>
                        <span class="metric-value-enhanced">${summary.avg_files_per_codebase}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">📈 Avg Lines/Codebase</span>
                        <span class="metric-value-enhanced">${summary.avg_lines_per_codebase.toLocaleString()}</span>
                    </div>`;

                    // Language distribution
                    if (summary.language_distribution && Object.keys(summary.language_distribution).length > 0) {
                        const topLanguages = Object.entries(summary.language_distribution)
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 3);

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🔤 Top Languages</span>
                            <span class="metric-value-enhanced">${topLanguages.map(([lang, count]) => `${lang} (${count})`).join(', ')}</span>
                        </div>`;
                    }

                    // Populate the separate individual codebases table section
                    console.log('🔍 About to call populateIndividualCodebasesTable');
                    populateIndividualCodebasesTable(data.codebases);
                    console.log('✅ populateIndividualCodebasesTable call completed');

                    // Fallback: try again after a short delay if element wasn't ready
                    setTimeout(() => {
                        const tableContainer = document.getElementById('individual-codebases-table');
                        if (tableContainer && tableContainer.innerHTML.includes('Loading individual codebase details')) {
                            console.log('🔄 Retrying table population after delay');
                            populateIndividualCodebasesTable(data.codebases);
                        }
                    }, 100);

                    // Update the codebase dropdown with enhanced information
                    const codebaseSelect = document.getElementById('codebase-select');

                    // PRESERVE CURRENT SELECTION - this is the key fix!
                    const currentSelection = codebaseSelect.value;
                    console.log(`🔧 [DROPDOWN] Preserving current selection: '${currentSelection}'`);

                    codebaseSelect.innerHTML = ''; // Clear existing options

                    // Add default empty option to prevent automatic selection
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select a codebase...';
                    defaultOption.disabled = true;
                    codebaseSelect.appendChild(defaultOption);

                    // Add detailed codebase information
                    let selectionFound = false;
                    Object.values(data.codebases).forEach(codebase => {
                        if (codebase.status === 'active') {
                            // Add to dropdown with file count and size
                            const option = document.createElement('option');
                            option.value = codebase.name;
                            const files = codebase.source_files || codebase.files || 0;
                            const size = codebase.source_size_formatted || codebase.size_formatted || '0 B';
                            option.textContent = `${codebase.name} (${files} files, ${size})`;
                            codebaseSelect.appendChild(option);

                            // Check if this was the previously selected codebase
                            if (codebase.name === currentSelection) {
                                selectionFound = true;
                            }
                        }
                    });

                    // RESTORE PREVIOUS SELECTION if it still exists
                    if (selectionFound && currentSelection) {
                        codebaseSelect.value = currentSelection;
                        console.log(`✅ [DROPDOWN] Restored selection: '${currentSelection}'`);
                    } else if (currentSelection) {
                        console.log(`⚠️ [DROPDOWN] Previous selection '${currentSelection}' no longer available`);
                    }

                    // If no valid codebases found (only the default option exists), update the default option
                    if (codebaseSelect.children.length === 1) {
                        // Update the default option to show no codebases available
                        defaultOption.textContent = 'No codebases available';
                        console.log('⚠️ [DROPDOWN] No active codebases found');
                    }

                    // Update Vector DB buttons based on current selection
                    updateVectorDBButtons();
                } else {
                    html += `<div class="error">Error: ${data.error}</div>`;

                    // Update dropdown with error state (preserve selection if possible)
                    const codebaseSelect = document.getElementById('codebase-select');
                    const currentSelection = codebaseSelect.value;
                    codebaseSelect.innerHTML = '<option value="" disabled>Error loading codebases</option>';
                    console.log(`❌ [DROPDOWN] Error loading codebases, previous selection was: '${currentSelection}'`);

                    // Update individual table with error
                    document.getElementById('individual-codebases-table').innerHTML = `<div class="error">Failed to load individual codebase details: ${data.error}</div>`;

                    // Update Vector DB buttons (will be disabled due to no selection)
                    updateVectorDBButtons();
                }

                document.getElementById('codebases-status').innerHTML = html;
            } catch (error) {
                document.getElementById('codebases-status').innerHTML = `<div class="error">Failed to load codebases: ${error.message}</div>`;

                // Update dropdown with error state (preserve selection if possible)
                const codebaseSelect = document.getElementById('codebase-select');
                const currentSelection = codebaseSelect.value;
                codebaseSelect.innerHTML = '<option value="" disabled>Failed to load codebases</option>';
                console.log(`❌ [DROPDOWN] Failed to load codebases, previous selection was: '${currentSelection}'`);

                // Update individual table with error
                document.getElementById('individual-codebases-table').innerHTML = `<div class="error">Failed to load individual codebase details: ${error.message}</div>`;

                // Update Vector DB buttons (will be disabled due to no selection)
                updateVectorDBButtons();
            }
        }

        function populateIndividualCodebasesTable(codebases) {
            // Populate the individual codebases table in its dedicated section
            console.log('🔍 populateIndividualCodebasesTable called with:', codebases);

            const tableContainer = document.getElementById('individual-codebases-table');

            if (!tableContainer) {
                console.error('❌ individual-codebases-table element not found!');
                return;
            }

            if (!codebases || Object.keys(codebases).length === 0) {
                console.log('⚠️ No codebase data available');
                tableContainer.innerHTML = '<div class="error">No codebase data available</div>';
                return;
            }

            // Create table header
            let tableHtml = `<div class="codebase-table">
                <div class="codebase-table-header">
                    <div class="codebase-col-name">📁 Name</div>
                    <div class="codebase-col-files">📄 Files</div>
                    <div class="codebase-col-lines">📏 Lines</div>
                    <div class="codebase-col-size">💾 Size</div>
                    <div class="codebase-col-avg">📊 Avg/File</div>
                    <div class="codebase-col-chunks">🗄️ Chunks</div>
                    <div class="codebase-col-languages">🔤 Languages</div>
                </div>`;

            // Add table rows for each active codebase
            const activeCodebases = Object.values(codebases).filter(cb => cb.status === 'active');

            if (activeCodebases.length === 0) {
                tableContainer.innerHTML = '<div class="error">No active codebases found</div>';
                return;
            }

            activeCodebases.forEach(codebase => {
                const sourceFiles = codebase.source_files || codebase.files || 0;
                const sourceLines = codebase.source_lines || codebase.lines || 0;
                const sourceSize = codebase.source_size_formatted || codebase.size_formatted || '0 B';
                const vectorChunks = codebase.vector_chunks || 0;
                const avgLines = codebase.avg_lines_per_file || 0;

                // Get top 2 languages for compact display
                const languages = codebase.languages || {};
                let languageDisplay = 'None';
                if (Object.keys(languages).length > 0) {
                    const topLangs = Object.entries(languages)
                        .sort(([,a], [,b]) => (b.files || 0) - (a.files || 0))
                        .slice(0, 2)
                        .map(([lang, data]) => `${lang} (${data.files || 0})`)
                        .join(', ');
                    languageDisplay = topLangs;
                    if (Object.keys(languages).length > 2) {
                        languageDisplay += ` +${Object.keys(languages).length - 2} more`;
                    }
                }

                tableHtml += `<div class="codebase-table-row">
                    <div class="codebase-col-name" data-label="📁 Name">
                        <strong>${codebase.name}</strong>
                    </div>
                    <div class="codebase-col-files" data-label="📄 Files">${sourceFiles.toLocaleString()}</div>
                    <div class="codebase-col-lines" data-label="📏 Lines">${sourceLines.toLocaleString()}</div>
                    <div class="codebase-col-size" data-label="💾 Size">${sourceSize}</div>
                    <div class="codebase-col-avg" data-label="📊 Avg/File">${avgLines}</div>
                    <div class="codebase-col-chunks" data-label="🗄️ Chunks">${vectorChunks.toLocaleString()}</div>
                    <div class="codebase-col-languages" data-label="🔤 Languages">${languageDisplay}</div>
                </div>`;
            });

            tableHtml += `</div>`; // Close codebase-table

            console.log('✅ Setting table HTML, length:', tableHtml.length);
            tableContainer.innerHTML = tableHtml;
            console.log('✅ Individual codebases table populated successfully');
        }

        async function loadGpuStatus() {
            try {
                const response = await fetch('/api/gpu');
                const data = await response.json();

                let html = '';
                if (data.success) {
                    const gpuData = data.data;
                    const summary = gpuData.infrastructure_summary || {};
                    const performance = gpuData.performance_metrics || {};

                    // Infrastructure status
                    html += `<div class="metric-enhanced metric-highlight">
                        <span class="metric-label-enhanced">🎮 Infrastructure</span>
                        <span class="metric-value-enhanced">${gpuData.gpu_infrastructure_available ? '✅ Available' : '❌ Unavailable'}</span>
                    </div>`;

                    // GPU count and types
                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">🔢 Total GPUs</span>
                        <span class="metric-value-enhanced">${gpuData.available_gpus || 0}</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">🏗️ Supported Types</span>
                        <span class="metric-value-enhanced">${gpuData.supported_gpu_types || 0}</span>
                    </div>`;

                    // Performance metrics
                    if (performance.available) {
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">💾 Total VRAM</span>
                            <span class="metric-value-enhanced">${performance.total_vram_gb}GB</span>
                        </div>`;

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">⚡ Avg Speed</span>
                            <span class="metric-value-enhanced">${performance.average_processing_speed}x</span>
                        </div>`;

                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🏛️ Architectures</span>
                            <span class="metric-value-enhanced">${performance.unique_architectures}</span>
                        </div>`;
                    }

                    // Deployment status
                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">🚀 Deployment Ready</span>
                        <span class="metric-value-enhanced">${summary.deployment_ready ? '✅ Yes' : '❌ No'}</span>
                    </div>`;

                    // Populate the detailed GPU table
                    populateGpuDetailsTable(gpuData);

                } else {
                    html += `<div class="error">Error: ${data.error}</div>`;
                    document.getElementById('gpu-details-table').innerHTML = `<div class="error">Failed to load GPU details: ${data.error}</div>`;
                }

                document.getElementById('gpu-status').innerHTML = html;
            } catch (error) {
                document.getElementById('gpu-status').innerHTML = `<div class="error">Failed to load GPU status: ${error.message}</div>`;
                document.getElementById('gpu-details-table').innerHTML = `<div class="error">Failed to load GPU details: ${error.message}</div>`;
            }
        }

        function populateGpuDetailsTable(gpuData) {
            // Populate the GPU details table in its dedicated section
            console.log('🎮 populateGpuDetailsTable called with:', gpuData);

            const tableContainer = document.getElementById('gpu-details-table');

            if (!tableContainer) {
                console.error('❌ gpu-details-table element not found!');
                return;
            }

            const gpuDetails = gpuData.gpu_details || {};
            const recommendations = gpuData.processing_recommendations || {};

            if (!gpuData.gpu_infrastructure_available || Object.keys(gpuDetails).length === 0) {
                tableContainer.innerHTML = '<div class="error">No GPU infrastructure available or no GPUs detected</div>';
                return;
            }

            // Create table header
            let tableHtml = `<div class="gpu-table">
                <div class="gpu-table-header">
                    <div class="gpu-col-host">🌐 Host</div>
                    <div class="gpu-col-type">🎮 GPU Type</div>
                    <div class="gpu-col-tier">🏆 Tier</div>
                    <div class="gpu-col-vram">💾 VRAM</div>
                    <div class="gpu-col-speed">⚡ Speed</div>
                    <div class="gpu-col-architecture">🏛️ Architecture</div>
                    <div class="gpu-col-recommendations">🎯 Recommendations</div>
                </div>`;

            // Add table rows for each GPU
            Object.entries(gpuDetails).forEach(([host, details]) => {
                const specs = details.specifications || {};
                const type = details.type || 'Unknown';
                const tier = details.tier || 'unknown';
                const vram = specs.vram || 'N/A';
                const speed = specs.processing_speed || 1.0;
                const architecture = specs.architecture || 'Unknown';

                // Format host URL for display
                const displayHost = host.replace('http://', '').replace('https://', '');

                // Get recommendations for this GPU
                let recommendationText = 'N/A';
                if (recommendations.recommendations_available && recommendations.recommendations) {
                    const rec = recommendations.recommendations;
                    if (rec.recommended_gpu && rec.recommended_gpu.host === host) {
                        const estimatedTime = rec.estimated_time_seconds || 0;
                        const strategy = rec.processing_strategy || 'unknown';
                        recommendationText = `${Math.round(estimatedTime)}s (${strategy})`;
                    }
                }

                tableHtml += `<div class="gpu-table-row">
                    <div class="gpu-col-host" data-label="🌐 Host">
                        <strong>${displayHost}</strong>
                    </div>
                    <div class="gpu-col-type" data-label="🎮 GPU Type">${type}</div>
                    <div class="gpu-col-tier" data-label="🏆 Tier">${tier}</div>
                    <div class="gpu-col-vram" data-label="💾 VRAM">${vram}</div>
                    <div class="gpu-col-speed" data-label="⚡ Speed">${speed}x</div>
                    <div class="gpu-col-architecture" data-label="🏛️ Architecture">${architecture}</div>
                    <div class="gpu-col-recommendations" data-label="🎯 Recommendations">${recommendationText}</div>
                </div>`;
            });

            tableHtml += `</div>`; // Close gpu-table

            console.log('✅ Setting GPU table HTML, length:', tableHtml.length);
            tableContainer.innerHTML = tableHtml;
            console.log('✅ GPU details table populated successfully');
        }

        async function loadMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();

                let html = '';
                if (data.success) {
                    const metrics = data.metrics;
                    const endpoint_perf = metrics.endpoint_performance;
                    const service_metrics = metrics.service_metrics;

                    // Endpoint performance
                    html += `<div class="metric-enhanced metric-highlight">
                        <span class="metric-label-enhanced">📊 Success Rate</span>
                        <span class="metric-value-enhanced">${endpoint_perf.success_rate_percent}%</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">⚡ Avg Response</span>
                        <span class="metric-value-enhanced">${endpoint_perf.average_response_time_ms}ms</span>
                    </div>`;

                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">🔗 Endpoints</span>
                        <span class="metric-value-enhanced">${endpoint_perf.successful_endpoints}/${endpoint_perf.total_endpoints_tested}</span>
                    </div>`;

                    // Service metrics
                    if (service_metrics && service_metrics.codebases) {
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">📁 Active Codebases</span>
                            <span class="metric-value-enhanced">${service_metrics.codebases.active}/${service_metrics.codebases.total}</span>
                        </div>`;
                    }

                    if (service_metrics && service_metrics.gpu_infrastructure) {
                        const gpu_status = service_metrics.gpu_infrastructure.available ? '✅ Available' : '❌ Unavailable';
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🎮 GPU Status</span>
                            <span class="metric-value-enhanced">${gpu_status}</span>
                        </div>`;
                    }

                    if (service_metrics && service_metrics.analysis_service) {
                        const analysis_status = service_metrics.analysis_service.available ? '✅ Available' : '❌ Unavailable';
                        html += `<div class="metric-enhanced">
                            <span class="metric-label-enhanced">🔍 Analysis Service</span>
                            <span class="metric-value-enhanced">${analysis_status}</span>
                        </div>`;
                    }

                    // Collection time
                    html += `<div class="metric-enhanced">
                        <span class="metric-label-enhanced">⏱️ Collection Time</span>
                        <span class="metric-value-enhanced">${metrics.collection_time_ms}ms</span>
                    </div>`;

                } else {
                    html += `<div class="error">Error: ${data.error}</div>`;
                }

                document.getElementById('metrics-status').innerHTML = html;
            } catch (error) {
                document.getElementById('metrics-status').innerHTML = `<div class="error">Failed to load metrics: ${error.message}</div>`;
            }
        }
        
        async function selectCodebase() {
            const codebaseName = document.getElementById('codebase-select').value;
            const outputDiv = document.getElementById('control-output');
            
            try {
                outputDiv.innerHTML = '<div class="loading">Selecting codebase...</div>';
                
                const response = await fetch('/api/control/select_codebase', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ codebase_name: codebaseName })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    outputDiv.innerHTML = `<div class="success">✅ Successfully selected codebase: ${codebaseName}</div>`;

                    // Reload test questions for the selected codebase
                    loadTestQuestions(codebaseName);

                    // Update Vector DB buttons for the selected codebase
                    updateVectorDBButtons();
                } else {
                    outputDiv.innerHTML = `<div class="error">❌ Failed to select codebase: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testQuery() {
            const query = document.getElementById('test-query').value;
            const codebaseName = document.getElementById('codebase-select').value;
            const resultsCount = parseInt(document.getElementById('results-count').value);
            const outputDiv = document.getElementById('control-output');

            if (!query.trim()) {
                outputDiv.innerHTML = '<div class="error">Please enter a test query</div>';
                return;
            }

            try {
                outputDiv.innerHTML = `<div class="loading">Running test query (${resultsCount} results)...</div>`;

                const response = await fetch('/api/test/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        codebase_name: codebaseName,
                        n_results: resultsCount
                    })
                });
                
                const data = await response.json();
                
                console.log('Query response data:', data); // Debug logging

                if (data.success) {
                    let html = `<div class="success">✅ Query executed successfully</div>`;
                    html += `<div class="metric">
                        <span class="metric-label">Query</span>
                        <span class="metric-value">${query || 'No query'}</span>
                    </div>`;
                    html += `<div class="metric">
                        <span class="metric-label">Codebase</span>
                        <span class="metric-value">${codebaseName || 'No codebase'}</span>
                    </div>`;
                    html += `<div class="metric">
                        <span class="metric-label">Results Found</span>
                        <span class="metric-value">${data.results_count || 0}</span>
                    </div>`;

                    if (data.results && Array.isArray(data.results) && data.results.length > 0) {
                        html += `<h4>📋 Code Results (${data.results.length}):</h4>`;
                        html += `<div class="code-results-container">`;

                        data.results.forEach((result, index) => {
                            const file = result.file || result.metadata?.file_path || result.metadata?.source_file || 'Unknown file';
                            const language = result.language || result.metadata?.language || result.metadata?.file_type || 'Unknown';
                            const content = result.content || 'No content available';

                            // Extract additional metadata if available
                            const metadata = result.metadata || {};
                            const chunk_id = metadata.chunk_id || '';
                            const chunk_type = metadata.chunk_type || '';

                            // Get file name and path
                            const fileName = file !== 'Unknown file' ? (file.split('/').pop() || file) : 'Unknown file';
                            const filePath = file.includes('/') ? file.substring(0, file.lastIndexOf('/')) : '';

                            // Language class for styling
                            const langClass = `lang-${language.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;

                            html += `<div class="code-result-tile ${langClass}">`;

                            // Header with file info and badges
                            html += `<div class="code-result-header">`;
                            html += `<div class="code-result-title">`;
                            html += `<span>📄</span>`;
                            html += `<span>${fileName}</span>`;
                            if (filePath) {
                                html += `<span style="font-size: 11px; opacity: 0.8;">/${filePath}</span>`;
                            }
                            html += `</div>`;
                            html += `<div class="code-result-badges">`;
                            html += `<span class="code-badge">${language}</span>`;
                            if (chunk_type) {
                                html += `<span class="code-badge">${chunk_type}</span>`;
                            }
                            html += `</div>`;
                            html += `</div>`;

                            // Content
                            html += `<div class="code-result-content">`;
                            html += `<div class="code-result-preview">${content}</div>`;

                            // Meta information
                            html += `<div class="code-result-meta">`;
                            html += `<div class="code-result-stats">`;
                            html += `<span>📏 ${content.length} chars</span>`;
                            html += `<span>📍 Result ${index + 1}</span>`;
                            if (chunk_id) {
                                html += `<span>🔗 ${chunk_id}</span>`;
                            }
                            html += `</div>`;
                            html += `<div class="code-result-actions">`;
                            html += `<button class="code-action-btn" onclick="copyToClipboard('${content.replace(/'/g, "\\'")}')">📋 Copy</button>`;
                            html += `<button class="code-action-btn" onclick="expandResult(${index})">🔍 Expand</button>`;
                            html += `</div>`;
                            html += `</div>`;

                            html += `</div>`;
                            html += `</div>`;
                        });

                        html += `</div>`;
                    } else {
                        html += `<div class="error">No code results found for this query</div>`;
                        html += `<div style="font-size: 12px; color: #666; margin-top: 10px;">`;
                        html += `Debug info: results=${data.results ? 'exists' : 'missing'}, `;
                        html += `isArray=${Array.isArray(data.results)}, `;
                        html += `length=${data.results ? data.results.length : 'N/A'}`;
                        html += `</div>`;
                    }

                    outputDiv.innerHTML = html;
                } else {
                    outputDiv.innerHTML = `<div class="error">❌ Query failed: ${data.error || 'Unknown error'}</div>`;
                    console.error('Query failed:', data);
                }
            } catch (error) {
                console.error('Query test error:', error);
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                outputDiv.innerHTML += `<div style="font-size: 12px; color: #666; margin-top: 10px;">`;
                outputDiv.innerHTML += `Debug: Check browser console for detailed error information`;
                outputDiv.innerHTML += `</div>`;
            }
        }
        
        async function restartServer() {
            const outputDiv = document.getElementById('control-output');

            try {
                outputDiv.innerHTML = '<div class="loading">Attempting server restart...</div>';

                const response = await fetch('/api/control/restart', {
                    method: 'POST'
                });

                const data = await response.json();
                outputDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function refreshGPUs() {
            const outputDiv = document.getElementById('control-output');

            try {
                outputDiv.innerHTML = '<div class="loading">🔄 Refreshing GPU infrastructure...</div>';

                const response = await fetch('/api/gpu/refresh', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.response.result || 'GPU infrastructure refreshed successfully';
                    const gpuCount = data.response.gpu_status?.available_gpus || 0;

                    outputDiv.innerHTML = `<div class="success">✅ GPU Refresh Successful</div><div class="log-output">${result}</div>`;

                    // Auto-refresh GPU status display
                    setTimeout(() => {
                        loadGPUStatus();
                    }, 1000);

                    // Show success message with GPU count
                    if (gpuCount > 1) {
                        const toast = document.createElement('div');
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #28a745;
                            color: white;
                            padding: 15px 20px;
                            border-radius: 5px;
                            z-index: 1000;
                            font-size: 14px;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                        `;
                        toast.innerHTML = `🎉 Found ${gpuCount} GPUs!`;
                        document.body.appendChild(toast);

                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 4000);
                    }
                } else {
                    const errorMsg = (data.response && data.response.error) || data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ GPU Refresh Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        // Test Questions Management Functions
        async function loadTestQuestions(codebaseName = null) {
            try {
                const url = codebaseName ? `/api/test_questions?codebase_name=${encodeURIComponent(codebaseName)}` : '/api/test_questions';
                const response = await fetch(url);
                const data = await response.json();

                const testQuestionsSelect = document.getElementById('test-questions-select');
                testQuestionsSelect.innerHTML = '<option value="">Select a preconfigured test question...</option>';

                if (data.success) {
                    if (codebaseName && data.codebase_name) {
                        // Single codebase mode
                        data.questions.forEach((question, index) => {
                            const option = document.createElement('option');
                            option.value = JSON.stringify({
                                codebase: data.codebase_name,
                                question: question,
                                index: index
                            });
                            option.textContent = question;
                            testQuestionsSelect.appendChild(option);
                        });

                        console.log(`Loaded ${data.questions.length} test questions for ${data.codebase_name}`);
                    } else {
                        // All codebases mode (fallback)
                        for (const [codebaseName, codebaseData] of Object.entries(data.codebases)) {
                            const optgroup = document.createElement('optgroup');
                            optgroup.label = `${codebaseName} (${codebaseData.languages.join(', ')})`;

                            codebaseData.questions.forEach((question, index) => {
                                const option = document.createElement('option');
                                option.value = JSON.stringify({
                                    codebase: codebaseName,
                                    question: question,
                                    index: index
                                });
                                option.textContent = question;
                                optgroup.appendChild(option);
                            });

                            testQuestionsSelect.appendChild(optgroup);
                        }

                        console.log(`Loaded ${data.total_questions} test questions for ${data.total_codebases} codebases`);
                    }
                } else {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = data.error || 'Error loading test questions';
                    option.disabled = true;
                    testQuestionsSelect.appendChild(option);
                }
            } catch (error) {
                console.error('Failed to load test questions:', error);
                const testQuestionsSelect = document.getElementById('test-questions-select');
                testQuestionsSelect.innerHTML = '<option value="" disabled>Failed to load test questions</option>';
            }
        }

        function loadTestQuestion() {
            const testQuestionsSelect = document.getElementById('test-questions-select');
            const selectedValue = testQuestionsSelect.value;

            if (selectedValue) {
                try {
                    const questionData = JSON.parse(selectedValue);

                    // Set the codebase
                    const codebaseSelect = document.getElementById('codebase-select');
                    codebaseSelect.value = questionData.codebase;

                    // Set the query
                    const testQueryInput = document.getElementById('test-query');
                    testQueryInput.value = questionData.question;

                    // Show a notification
                    const outputDiv = document.getElementById('control-output');
                    outputDiv.innerHTML = `<div class="success">✅ Loaded test question for ${questionData.codebase}: "${questionData.question}"</div>`;

                    console.log(`Loaded test question: ${questionData.question} for codebase: ${questionData.codebase}`);
                } catch (error) {
                    console.error('Error parsing test question data:', error);
                }
            }
        }

        async function reprocessAllCodebases() {
            const outputDiv = document.getElementById('control-output');

            if (!confirm('Reprocess embeddings for ALL source code repositories?\n\nThis will update all vector databases and may take a very long time (several minutes per codebase).\n\nThis operation cannot be cancelled once started.')) {
                return;
            }

            try {
                outputDiv.innerHTML = '<div class="loading">🔄 Starting bulk reprocessing... Progress will be shown above.</div>';

                // Show progress container
                const progressContainer = document.getElementById('progress-container');
                progressContainer.style.display = 'block';
                document.getElementById('progress-title').textContent = 'Bulk Reprocessing All Codebases';
                document.getElementById('progress-text').textContent = 'Initializing bulk reprocessing...';
                document.getElementById('progress-bar').style.width = '0%';

                const response = await fetch('/api/vector_db/reprocess_all', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    const summary = data.summary || 'All codebases reprocessed successfully';
                    const successCount = data.successful_count || 0;
                    const failedCount = data.failed_count || 0;
                    const totalCount = data.total_codebases || 0;

                    outputDiv.innerHTML = `<div class="success">✅ Bulk Reprocessing Complete</div><div class="log-output">${summary}</div>`;

                    // Auto-refresh codebase status after completion
                    setTimeout(() => {
                        loadCodebasesStatus();
                    }, 2000);

                    // Show success notification
                    const toast = document.createElement('div');
                    toast.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: ${failedCount > 0 ? '#ffc107' : '#28a745'};
                        color: white;
                        padding: 15px 20px;
                        border-radius: 5px;
                        z-index: 1000;
                        font-size: 14px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                        max-width: 300px;
                    `;
                    toast.innerHTML = `
                        <strong>Bulk Reprocessing Complete!</strong><br>
                        ✅ Success: ${successCount}/${totalCount}<br>
                        ${failedCount > 0 ? `❌ Failed: ${failedCount}` : 'All codebases updated!'}
                    `;
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 8000);

                } else {
                    const errorMsg = data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ Bulk Reprocessing Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        // Update Vector Database Management buttons based on codebase selection
        function updateVectorDBButtons() {
            const codebaseName = document.getElementById('codebase-select').value;
            const createBtn = document.getElementById('create-btn');
            const rebuildBtn = document.getElementById('rebuild-btn');
            const reprocessBtn = document.getElementById('reprocess-btn');
            const deleteBtn = document.getElementById('delete-btn');

            console.log(`🔧 [VECTOR_DB_BUTTONS] Updating buttons for codebase: '${codebaseName}'`);

            if (codebaseName && codebaseName.trim() !== '') {
                // Enable buttons and update labels with selected codebase
                createBtn.disabled = false;
                createBtn.innerHTML = `➕ Create: ${codebaseName}`;
                createBtn.title = `Create new vector database for ${codebaseName}`;

                rebuildBtn.disabled = false;
                rebuildBtn.innerHTML = `🔄 Rebuild: ${codebaseName}`;
                rebuildBtn.title = `Delete and recreate vector database for ${codebaseName}`;

                reprocessBtn.disabled = false;
                reprocessBtn.innerHTML = `⚙️ Reprocess: ${codebaseName}`;
                reprocessBtn.title = `Update existing vector database for ${codebaseName}`;

                deleteBtn.disabled = false;
                deleteBtn.innerHTML = `🗑️ Delete: ${codebaseName}`;
                deleteBtn.title = `Remove vector database for ${codebaseName}`;
            } else {
                // Disable buttons and reset to default labels
                createBtn.disabled = true;
                createBtn.innerHTML = '➕ Create New';
                createBtn.title = 'Select a codebase first';

                rebuildBtn.disabled = true;
                rebuildBtn.innerHTML = '🔄 Rebuild';
                rebuildBtn.title = 'Select a codebase first';

                reprocessBtn.disabled = true;
                reprocessBtn.innerHTML = '⚙️ Reprocess';
                reprocessBtn.title = 'Select a codebase first';

                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '🗑️ Delete';
                deleteBtn.title = 'Select a codebase first';
            }
        }

        // Vector Database Management Functions
        async function createVectorDB() {
            const codebaseName = document.getElementById('codebase-select').value;
            const outputDiv = document.getElementById('control-output');

            if (!codebaseName) {
                outputDiv.innerHTML = '<div class="error">Please select a codebase first</div>';
                return;
            }

            if (!confirm(`Create new vector database for codebase '${codebaseName}'?\n\nThis will fail if the codebase already exists.`)) {
                return;
            }

            try {
                outputDiv.innerHTML = '<div class="loading">🔄 Creating vector database... This may take several minutes.</div>';

                const response = await fetch('/api/vector_db/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        codebase_name: codebaseName,
                        exclude_dirs: ["build", "test", "bin", "obj", "__pycache__", ".git"]
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.response.result || 'Vector database created successfully';
                    outputDiv.innerHTML = `<div class="success">✅ Create Successful</div><div class="log-output">${result}</div>`;
                    // Refresh codebase list
                    setTimeout(() => loadCodebasesStatus(), 2000);
                } else {
                    const errorMsg = (data.response && data.response.error) || data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ Create Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function rebuildVectorDB() {
            const codebaseName = document.getElementById('codebase-select').value;
            const outputDiv = document.getElementById('control-output');

            if (!codebaseName) {
                outputDiv.innerHTML = '<div class="error">Please select a codebase first</div>';
                return;
            }

            if (!confirm(`Rebuild vector database for codebase '${codebaseName}'?\n\nThis will DELETE all existing data and recreate it. This action cannot be undone!`)) {
                return;
            }

            try {
                outputDiv.innerHTML = '<div class="loading">🔄 Rebuilding vector database... This may take several minutes.</div>';

                const response = await fetch('/api/vector_db/rebuild', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        codebase_name: codebaseName,
                        exclude_dirs: ["build", "test", "bin", "obj", "__pycache__", ".git"]
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.response.result || 'Vector database rebuilt successfully';
                    outputDiv.innerHTML = `<div class="success">✅ Rebuild Successful</div><div class="log-output">${result}</div>`;
                    // Refresh codebase list
                    setTimeout(() => loadCodebasesStatus(), 2000);
                } else {
                    const errorMsg = (data.response && data.response.error) || data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ Rebuild Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function reprocessVectorDB() {
            const codebaseName = document.getElementById('codebase-select').value;
            const outputDiv = document.getElementById('control-output');

            if (!codebaseName) {
                outputDiv.innerHTML = '<div class="error">Please select a codebase first</div>';
                return;
            }

            if (!confirm(`Reprocess vector database for codebase '${codebaseName}'?\n\nThis will update the existing database with any changes.`)) {
                return;
            }

            try {
                outputDiv.innerHTML = '<div class="loading">🔄 Reprocessing vector database... This may take several minutes.</div>';

                const response = await fetch('/api/vector_db/reprocess', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        codebase_name: codebaseName,
                        exclude_dirs: ["build", "test", "bin", "obj", "__pycache__", ".git"]
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.response.result || 'Vector database reprocessed successfully';
                    outputDiv.innerHTML = `<div class="success">✅ Reprocess Successful</div><div class="log-output">${result}</div>`;
                    // Refresh codebase list
                    setTimeout(() => loadCodebasesStatus(), 2000);
                } else {
                    const errorMsg = (data.response && data.response.error) || data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ Reprocess Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function deleteVectorDB() {
            const codebaseName = document.getElementById('codebase-select').value;
            const outputDiv = document.getElementById('control-output');

            if (!codebaseName) {
                outputDiv.innerHTML = '<div class="error">Please select a codebase first</div>';
                return;
            }

            if (!confirm(`DELETE vector database for codebase '${codebaseName}'?\n\nThis will permanently remove all indexed data. This action cannot be undone!\n\nType the codebase name to confirm: ${codebaseName}`)) {
                return;
            }

            const confirmName = prompt(`Please type the codebase name '${codebaseName}' to confirm deletion:`);
            if (confirmName !== codebaseName) {
                outputDiv.innerHTML = '<div class="error">❌ Deletion cancelled - codebase name did not match</div>';
                return;
            }

            try {
                outputDiv.innerHTML = '<div class="loading">🗑️ Deleting vector database...</div>';

                const response = await fetch('/api/vector_db/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ codebase_name: codebaseName })
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.response.result || 'Vector database deleted successfully';
                    outputDiv.innerHTML = `<div class="success">✅ Delete Successful</div><div class="log-output">${result}</div>`;
                    // Refresh codebase list
                    setTimeout(() => loadCodebasesStatus(), 2000);
                } else {
                    const errorMsg = (data.response && data.response.error) || data.error || 'Unknown error';
                    outputDiv.innerHTML = `<div class="error">❌ Delete Failed: ${errorMsg}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        // Helper functions for code result tiles
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show temporary success message
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    z-index: 1000;
                    font-size: 14px;
                `;
                toast.textContent = '📋 Code copied to clipboard!';
                document.body.appendChild(toast);

                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                alert('Failed to copy to clipboard');
            });
        }

        function expandResult(index) {
            // Find the result tile and toggle expanded state
            const tiles = document.querySelectorAll('.code-result-tile');
            if (tiles[index]) {
                const preview = tiles[index].querySelector('.code-result-preview');
                const expandBtn = tiles[index].querySelector('.code-action-btn:last-child');

                if (preview.style.maxHeight === 'none') {
                    // Collapse
                    preview.style.maxHeight = '200px';
                    expandBtn.textContent = '🔍 Expand';
                } else {
                    // Expand
                    preview.style.maxHeight = 'none';
                    expandBtn.textContent = '📦 Collapse';
                }
            }
        }

        // === HYBRID CODE ANALYSIS FUNCTIONS ===

        async function loadHybridModels() {
            try {
                const response = await fetch('/api/hybrid/models');
                const data = await response.json();

                if (data.success) {
                    displayHybridModels(data.models);
                } else {
                    document.getElementById('model-grid').innerHTML =
                        '<div class="error">Hybrid analysis not available: ' + data.error + '</div>';
                }
            } catch (error) {
                document.getElementById('model-grid').innerHTML =
                    '<div class="error">Failed to load models: ' + error.message + '</div>';
            }
        }

        function displayHybridModels(models) {
            const modelGrid = document.getElementById('model-grid');

            let html = '';

            // Local models
            if (models.local_models && models.local_models.length > 0) {
                html += '<div class="model-group">';
                html += '<h5>🏠 Local Models (OpenWebUI)</h5>';

                models.local_models.forEach((model, index) => {
                    const isFirst = index === 0;
                    html += `
                        <div class="model-option">
                            <input type="radio" name="hybrid-model" value="${model.name}" id="local-${index}" ${isFirst ? 'checked' : ''}>
                            <label for="local-${index}">${model.name} (${model.location})</label>
                        </div>
                    `;
                });

                html += '</div>';
            }

            // Remote models
            if (models.remote_models && models.remote_models.length > 0) {
                html += '<div class="model-group">';
                html += '<h5>🌐 Remote Models (Direct Ollama)</h5>';

                models.remote_models.forEach((model, index) => {
                    const fastestLabel = model.gpu === 'RTX 3090' ? ' ⚡ Fastest' : '';
                    html += `
                        <div class="model-option">
                            <input type="radio" name="hybrid-model" value="${model.name}" id="remote-${index}">
                            <label for="remote-${index}">${model.location} (${model.gpu})${fastestLabel}</label>
                        </div>
                    `;
                });

                // Add auto-select option
                html += `
                    <div class="model-option">
                        <input type="radio" name="hybrid-model" value="auto" id="auto-select">
                        <label for="auto-select">🎯 Auto-select (prefer fast)</label>
                    </div>
                `;

                html += '</div>';
            }

            if (html === '') {
                html = '<div class="error">No models available</div>';
            }

            modelGrid.innerHTML = html;
        }

        async function runHybridAnalysis() {
            const query = document.getElementById('hybrid-query').value.trim();
            const selectedModel = document.querySelector('input[name="hybrid-model"]:checked');
            const preferFast = document.getElementById('prefer-fast').checked;

            if (!query) {
                alert('Please enter a query');
                return;
            }

            if (!selectedModel) {
                alert('Please select a model');
                return;
            }

            // Show loading status
            const resultsDiv = document.getElementById('hybrid-results');
            resultsDiv.style.display = 'block';

            const statusHtml = '<div class="status-indicator loading">🔄 Processing query...</div>';
            document.getElementById('analysis-content').innerHTML = statusHtml;
            document.getElementById('performance-metrics').innerHTML = '';

            const startTime = Date.now();

            try {
                const response = await fetch('/api/hybrid/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        model: selectedModel.value,
                        prefer_fast: preferFast
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const clientResponseTime = (endTime - startTime) / 1000;

                if (data.success) {
                    // Show success status
                    const successHtml = '<div class="status-indicator success">✅ Analysis complete!</div>';

                    // Show performance metrics
                    const metricsHtml = `
                        <div class="metric-card">
                            <div class="metric-label">Response Time</div>
                            <div class="metric-value">${data.response_time || clientResponseTime.toFixed(2)}s</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Backend</div>
                            <div class="metric-value">${data.backend || 'unknown'}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Model</div>
                            <div class="metric-value">${data.model || selectedModel.value}</div>
                        </div>
                    `;

                    document.getElementById('performance-metrics').innerHTML = metricsHtml;
                    document.getElementById('analysis-content').innerHTML = successHtml + data.result;

                } else {
                    // Show error
                    const errorHtml = '<div class="status-indicator error">❌ Error: ' + data.error + '</div>';
                    document.getElementById('analysis-content').innerHTML = errorHtml;
                }

            } catch (error) {
                const errorHtml = '<div class="status-indicator error">❌ Network error: ' + error.message + '</div>';
                document.getElementById('analysis-content').innerHTML = errorHtml;
            }
        }

        // Load hybrid models when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add to existing DOMContentLoaded if it exists, or create new one
            loadHybridModels();
        });
    </script>
</body>
</html>
