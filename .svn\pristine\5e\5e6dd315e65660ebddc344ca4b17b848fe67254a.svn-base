#!/usr/bin/env python3
"""
Debug script to check what chunks are being retrieved for utils codebase
"""

import sys
sys.path.append('.')
from main import code_analyzer_service

def debug_utils_search():
    print("🔍 Debugging utils codebase search...")

    # First, let's check what codebases are available
    try:
        codebases = code_analyzer_service.list_codebases()
        print(f"Available codebases: {codebases}")
    except Exception as e:
        print(f"Error listing codebases: {e}")

    # Test search for utils codebase
    try:
        chunks = code_analyzer_service.search('memory management', 'utils', n_results=5)
        print(f'\nFound {len(chunks)} chunks for "memory management":')

        for i, chunk in enumerate(chunks):
            print(f'\nChunk {i+1}:')
            print(f'  File: {chunk["metadata"].get("relative_path", "unknown")}')
            print(f'  Language: {chunk["metadata"].get("language", "unknown")}')
            print(f'  Content preview: {chunk["content"][:200]}...')

    except Exception as e:
        print(f"Error searching utils: {e}")
        import traceback
        traceback.print_exc()

    # Test another search
    try:
        chunks = code_analyzer_service.search('what does this codebase implement', 'utils', n_results=10)
        print(f'\n\nFound {len(chunks)} chunks for "what does this codebase implement":')

        for i, chunk in enumerate(chunks):
            print(f'\nChunk {i+1}:')
            print(f'  File: {chunk["metadata"].get("relative_path", "unknown")}')
            print(f'  Language: {chunk["metadata"].get("language", "unknown")}')
            print(f'  Content preview: {chunk["content"][:200]}...')

    except Exception as e:
        print(f"Error searching utils: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_utils_search()
