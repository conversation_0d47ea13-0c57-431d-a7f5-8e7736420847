{".class": "MypyFile", "_fullname": "test_semantic_analysis", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EnhancedQueryProcessor": {".class": "SymbolTableNode", "cross_ref": "semantic_chunking_enhancement.EnhancedQueryProcessor", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "SemanticAnalysisTestSuite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite", "name": "SemanticAnalysisTestSuite", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_semantic_analysis", "mro": ["test_semantic_analysis.SemanticAnalysisTestSuite", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.__init__", "name": "__init__", "type": null}}, "get_sample_c_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.get_sample_c_code", "name": "get_sample_c_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["test_semantic_analysis.SemanticAnalysisTestSuite"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sample_c_code of SemanticAnalysisTestSuite", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log_test_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "test_name", "success", "details", "execution_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.log_test_result", "name": "log_test_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "test_name", "success", "details", "execution_time"], "arg_types": ["test_semantic_analysis.SemanticAnalysisTestSuite", "builtins.str", "builtins.bool", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_test_result of SemanticAnalysisTestSuite", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_all_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.run_all_tests", "name": "run_all_tests", "type": null}}, "semantic_chunker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.semantic_chunker", "name": "semantic_chunker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_analyze_codebase_semantics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_analyze_codebase_semantics", "name": "test_analyze_codebase_semantics", "type": null}}, "test_query_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_query_processor", "name": "test_query_processor", "type": null}}, "test_real_codebase_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_real_codebase_analysis", "name": "test_real_codebase_analysis", "type": null}}, "test_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_results", "name": "test_results", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_semantic_chunker_initialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_semantic_chunker_initialization", "name": "test_semantic_chunker_initialization", "type": null}}, "test_semantic_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.test_semantic_integration", "name": "test_semantic_integration", "type": null}}, "tree_sitter_chunker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.tree_sitter_chunker", "name": "tree_sitter_chunker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_semantic_analysis.SemanticAnalysisTestSuite.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_semantic_analysis.SemanticAnalysisTestSuite", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SemanticChunker": {".class": "SymbolTableNode", "cross_ref": "semantic_chunking_enhancement.SemanticChunker", "kind": "Gdef"}, "TreeSitterChunker": {".class": "SymbolTableNode", "cross_ref": "tree_sitter_chunker.TreeSitterChunker", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_semantic_analysis.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "f": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_semantic_analysis.f", "name": "f", "type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}}}, "integrate_semantic_chunking": {".class": "SymbolTableNode", "cross_ref": "semantic_chunking_enhancement.integrate_semantic_chunking", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "results": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_semantic_analysis.results", "name": "results", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "test_suite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_semantic_analysis.test_suite", "name": "test_suite", "type": "test_semantic_analysis.SemanticAnalysisTestSuite"}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_semantic_analysis.py"}