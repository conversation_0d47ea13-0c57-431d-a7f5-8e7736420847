["test_openwebui_api.py::test_with_codebase_selection", "test_openwebui_api.py::test_without_codebase_selection", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analysis_health", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analysis_status", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_analyze_codebase_tool_endpoint", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_enhanced_ask_endpoint", "unit-tests/test_analysis_endpoints.py::TestAnalysisEndpoints::test_enhanced_context_endpoint", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_health_endpoint_basic", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_list_codebases_basic", "unit-tests/test_basic_connectivity.py::TestBasicConnectivity::test_server_is_running", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_get_enhanced_stats", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_legacy_codebases_endpoint", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_list_codebases_get_method", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_select_codebase", "unit-tests/test_codebase_management.py::TestCodebaseManagement::test_session_codebase_management", "unit-tests/test_codebase_processing.py::TestBatchCodebaseProcessing::test_bulk_rebuild_statistics", "unit-tests/test_codebase_processing.py::TestBatchCodebaseProcessing::test_processing_error_handling", "unit-tests/test_codebase_processing.py::TestBatchCodebaseProcessing::test_processing_with_different_exclude_patterns", "unit-tests/test_codebase_processing.py::TestBatchCodebaseProcessing::test_rebuild_vs_process_comparison", "unit-tests/test_codebase_processing.py::TestBatchCodebaseProcessing::test_sequential_processing_multiple_codebases", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_bulk_rebuild_all_codebases", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_concurrent_processing_safety", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_create_new_codebase", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_list_available_codebases", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_process_individual_codebase", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_processing_nonexistent_codebase", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_processing_performance", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_processing_statistics_validation", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_processing_with_custom_exclude_dirs", "unit-tests/test_codebase_processing.py::TestCodebaseProcessing::test_rebuild_individual_codebase", "unit-tests/test_configuration.py::TestConfiguration::test_api_endpoint_accessibility", "unit-tests/test_configuration.py::TestConfiguration::test_api_versioning_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_content_encoding_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_cors_and_headers_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_environment_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_error_handling_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_health_check_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_json_response_format_consistency", "unit-tests/test_configuration.py::TestConfiguration::test_logging_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_ollama_integration_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_rate_limiting_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_security_headers", "unit-tests/test_configuration.py::TestConfiguration::test_server_configuration_completeness", "unit-tests/test_configuration.py::TestConfiguration::test_service_discovery_configuration", "unit-tests/test_configuration.py::TestConfiguration::test_timeout_configuration", "unit-tests/test_data_quality.py::TestDataQuality::test_code_authenticity_across_codebases", "unit-tests/test_data_quality.py::TestDataQuality::test_code_snippet_authenticity", "unit-tests/test_data_quality.py::TestDataQuality::test_enhanced_vs_legacy_quality", "unit-tests/test_data_quality.py::TestDataQuality::test_memory_management_query_authenticity", "unit-tests/test_data_quality.py::TestDataQuality::test_metadata_quality_indicators", "unit-tests/test_data_quality.py::TestDataQuality::test_response_consistency", "unit-tests/test_data_quality.py::TestDataQuality::test_source_attribution", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_chunk_count_accuracy", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_codebase_complexity_analysis", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_codebase_enhancement_status", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_codebase_processing_capability", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_database_availability", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_deployment_configuration", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_deployment_health_indicators", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_enhanced_features_availability", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_enhancement_recommendation_system", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_feature_completeness", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_last_updated_tracking", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_metadata_version_validation", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_multi_language_detection", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_optimization_features", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_source_path_validation", "unit-tests/test_deployment_features.py::TestDeploymentFeatures::test_version_information", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_analyze_codebase_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_enhance_query_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_query", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_framework_status", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_get_patterns_endpoint", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_recommendations", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_refresh", "unit-tests/test_framework_integration.py::TestFrameworkIntegration::test_gpu_status", "unit-tests/test_intent_detection.py::TestIntentDetection::test_detect_intent", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_config", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_detection_empty_query", "unit-tests/test_intent_detection.py::TestIntentDetection::test_intent_detection_with_various_queries", "unit-tests/test_intent_detection.py::TestIntentDetection::test_reload_intent_config", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_all_languages_info", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_languages_by_category", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_languages_list", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_nonexistent_category", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_nonexistent_language", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_get_specific_language_details", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_language_categories_structure", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_language_descriptions", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_language_endpoint_performance", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_language_extensions_mapping", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_processor_types_distribution", "unit-tests/test_language_endpoints.py::TestLanguageEndpoints::test_tree_sitter_support_flags", "unit-tests/test_language_support.py::TestLanguageSupport::test_framework_language_coverage", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_detection_capability", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_extension_mapping", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[cpp-void test() {}]", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[go-func test() {}]", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[java-public void test() {}]", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[javascript-function test() {}]", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[python-def test(): pass]", "unit-tests/test_language_support.py::TestLanguageSupport::test_language_specific_queries[rust-fn test() {}]", "unit-tests/test_language_support.py::TestLanguageSupport::test_multi_language_codebase_support", "unit-tests/test_language_support.py::TestLanguageSupport::test_supported_languages_endpoint", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_code_analyzer_tool_availability", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_codebase_selection_workflow", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_error_handling_for_openwebui", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_framework_status_for_openwebui", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_intent_detection_integration", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_large_response_handling", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_openwebui_server_accessibility", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_response_format_compatibility", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_search_workflow_compatibility", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_session_management_compatibility", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_tool_configuration_compatibility", "unit-tests/test_openwebui_integration.py::TestOpenWebUIIntegration::test_tool_endpoint_compatibility", "unit-tests/test_performance.py::TestPerformance::test_basic_response_time", "unit-tests/test_performance.py::TestPerformance::test_codebase_listing_performance", "unit-tests/test_performance.py::TestPerformance::test_concurrent_requests_performance", "unit-tests/test_performance.py::TestPerformance::test_framework_status_performance", "unit-tests/test_performance.py::TestPerformance::test_gpu_status_performance", "unit-tests/test_performance.py::TestPerformance::test_intent_detection_performance", "unit-tests/test_performance.py::TestPerformance::test_large_result_set_performance", "unit-tests/test_performance.py::TestPerformance::test_response_time_consistency", "unit-tests/test_performance.py::TestPerformance::test_search_endpoint_performance", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_context_endpoint", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_enhanced_search", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_get_optimized_context", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_ask_about_code", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_legacy_search_code", "unit-tests/test_search_functionality.py::TestSearchFunctionality::test_search_endpoint_stateless", "unit-tests/test_search_ranking.py::TestSearchRanking::test_empty_query_handling", "unit-tests/test_search_ranking.py::TestSearchRanking::test_enhanced_search_endpoint", "unit-tests/test_search_ranking.py::TestSearchRanking::test_fallback_behavior", "unit-tests/test_search_ranking.py::TestSearchRanking::test_language_specific_ranking", "unit-tests/test_search_ranking.py::TestSearchRanking::test_query_intent_classification", "unit-tests/test_search_ranking.py::TestSearchRanking::test_search_ranking_configuration", "unit-tests/test_search_ranking.py::TestSearchRanking::test_search_ranking_metadata", "unit-tests/test_search_ranking.py::TestSearchRanking::test_search_ranking_performance", "unit-tests/test_search_ranking.py::TestSearchRanking::test_search_result_scoring", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_framework_integration_uses_semantic_chunking", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_non_cpp_language_bypass", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_semantic_chunker_direct_functionality", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_semantic_chunking_fallback_behavior", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_semantic_chunking_integration_basic", "unit-tests/test_semantic_chunking_integration.py::TestSemanticChunkingIntegration::test_semantic_chunking_metadata_preservation", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_framework_integration_semantic_chunking", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_non_cpp_files_bypass_semantic", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_query_processor_enhancement", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_real_codebase_file", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_semantic_analysis_basic", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_semantic_chunker_initialization", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_semantic_chunking_with_different_languages", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_semantic_integration_with_chunker", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_semantic_metadata_content_matching", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_analyze_endpoint_with_semantic", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_api_enhance_query", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_codebase_list_and_semantic_readiness", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_enhanced_search", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_integration_health", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_query_enhancement", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_search_endpoint", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_semantic_analysis_via_search", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_semantic_chunking_endpoint", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_tools_analyze_codebase", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_server_tools_list_codebases", "unit-tests/test_semantic_integration.py::TestSemanticIntegration::test_tree_sitter_chunker_basic_vs_semantic", "unit-tests/test_server_health.py::TestServerHealth::test_detailed_health_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_enhanced_features_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_health_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_root_endpoint", "unit-tests/test_server_health.py::TestServerHealth::test_status_endpoint", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_async_functionality", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_basic_math", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_configuration_values", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_exception_handling", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_list_operations", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[1-2]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[2-4]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[3-6]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_parametrized_test[4-8]", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_pytest_markers", "unit-tests/test_simple_verification.py::TestSimpleVerification::test_string_operations", "unit-tests/test_web_interface_integration.py::TestWebInterfaceIntegration::test_web_interface_accessibility", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_codebases_api_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_cors_headers", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_dashboard_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_debug_codebase_selection", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_detailed_codebases_api", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_error_handling", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_gpu_api_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_gpu_refresh_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_health_api_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_hybrid_models_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_metrics_api_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_performance_response_times", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_query_testing_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_response_format_consistency", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_session_codebase_management", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_test_questions_endpoint", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_vector_db_management_endpoints", "unit-tests/test_web_management_server.py::TestWebManagementServer::test_web_server_accessibility"]