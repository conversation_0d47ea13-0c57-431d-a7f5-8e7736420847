{".class": "MypyFile", "_fullname": "search_ranking_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChunkFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "search_ranking_integration.ChunkFilter", "name": "Chunk<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "search_ranking_integration.ChunkFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "search_ranking_integration", "mro": ["search_ranking_integration.ChunkFilter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.ChunkFilter.__init__", "name": "__init__", "type": null}}, "_has_too_many_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.ChunkFilter._has_too_many_functions", "name": "_has_too_many_functions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "threshold"], "arg_types": ["search_ranking_integration.ChunkFilter", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has_too_many_functions of ChunkFilter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_architectural_overview": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.ChunkFilter._is_architectural_overview", "name": "_is_architectural_overview", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "metadata"], "arg_types": ["search_ranking_integration.ChunkFilter", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_architectural_overview of ChunkFilter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "architectural_indicators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "search_ranking_integration.ChunkFilter.architectural_indicators", "name": "architectural_indicators", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "filter_chunks_for_function_queries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunks", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.ChunkFilter.filter_chunks_for_function_queries", "name": "filter_chunks_for_function_queries", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunks", "query"], "arg_types": ["search_ranking_integration.ChunkFilter", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_chunks_for_function_queries of ChunkFilter", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "search_ranking_integration.ChunkFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "search_ranking_integration.ChunkFilter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentType": {".class": "SymbolTableNode", "cross_ref": "generic_search_ranking.ContentType", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GenericSearchContext": {".class": "SymbolTableNode", "cross_ref": "generic_search_ranking.GenericSearchContext", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QueryIntent": {".class": "SymbolTableNode", "cross_ref": "generic_search_ranking.QueryIntent", "kind": "Gdef"}, "QueryPreprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "search_ranking_integration.QueryPreprocessor", "name": "QueryPreprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "search_ranking_integration.QueryPreprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "search_ranking_integration", "mro": ["search_ranking_integration.QueryPreprocessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.QueryPreprocessor.__init__", "name": "__init__", "type": null}}, "concept_synonyms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "search_ranking_integration.QueryPreprocessor.concept_synonyms", "name": "concept_synonyms", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "preprocess_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.QueryPreprocessor.preprocess_query", "name": "preprocess_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["search_ranking_integration.QueryPreprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess_query of QueryPreprocessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "search_ranking_integration.QueryPreprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "search_ranking_integration.QueryPreprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SearchRankingIntegrator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "search_ranking_integration.SearchRankingIntegrator", "name": "SearchRankingIntegrator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "search_ranking_integration.SearchRankingIntegrator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "search_ranking_integration", "mro": ["search_ranking_integration.SearchRankingIntegrator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.SearchRankingIntegrator.__init__", "name": "__init__", "type": null}}, "create_ranking_configuration_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.SearchRankingIntegrator.create_ranking_configuration_endpoint", "name": "create_ranking_configuration_endpoint", "type": null}}, "enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "search_ranking_integration.SearchRankingIntegrator.enabled", "name": "enabled", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "enhance_main_search_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.SearchRankingIntegrator.enhance_main_search_endpoint", "name": "enhance_main_search_endpoint", "type": null}}, "enhance_search_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code_analyzer_service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.SearchRankingIntegrator.enhance_search_service", "name": "enhance_search_service", "type": null}}, "ranker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "search_ranking_integration.SearchRankingIntegrator.ranker", "name": "ranker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "search_ranking_integration.SearchRankingIntegrator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "search_ranking_integration.SearchRankingIntegrator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "search_ranking_integration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_search_enhancement_wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["code_analyzer_service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.create_search_enhancement_wrapper", "name": "create_search_enhancement_wrapper", "type": null}}, "generic_ranker": {".class": "SymbolTableNode", "cross_ref": "generic_search_ranking.generic_ranker", "kind": "Gdef"}, "integrate_enhanced_ranking": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["code_analyzer_service", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "search_ranking_integration.integrate_enhanced_ranking", "name": "integrate_enhanced_ranking", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "search_ranking_integration.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\search_ranking_integration.py"}