#!/bin/bash
# Unit test runner for Linux/Mac
# This script runs the unit tests for the code analysis server

echo "========================================"
echo "OpenWebUI RAG Code Server Unit Tests"
echo "========================================"
echo

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "ERROR: Python is not installed or not in PATH"
    exit 1
fi

# Check if server is running
echo "Checking if server is running on home-ai-server.local:5002..."
if ! python -c "import requests; requests.get('http://home-ai-server.local:5002/health', timeout=5)" &> /dev/null; then
    echo "WARNING: Server may not be running on home-ai-server.local:5002"
    echo "Please start the server before running tests"
    echo
    read -p "Continue anyway? (y/n): " choice
    if [[ ! "$choice" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo
echo "Running unit tests..."
echo "========================================"

# Run the tests
python unit-tests/run_tests.py --verbose

echo
echo "========================================"
echo "Test run completed"
echo "========================================"
