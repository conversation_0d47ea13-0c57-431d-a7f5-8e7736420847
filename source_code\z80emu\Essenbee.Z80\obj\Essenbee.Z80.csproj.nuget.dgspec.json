{"format": 1, "restore": {"C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj": {}}, "projects": {"C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj": {"version": "1.0.1", "restore": {"projectUniqueName": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj", "projectName": "Essenbee.Z80", "projectPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.FxCopAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.9.8, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.315\\RuntimeIdentifierGraph.json"}}}}}