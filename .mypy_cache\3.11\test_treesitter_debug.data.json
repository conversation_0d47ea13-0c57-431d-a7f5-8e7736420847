{".class": "MypyFile", "_fullname": "test_treesitter_debug", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Language": {".class": "SymbolTableNode", "cross_ref": "tree_sitter.Language", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "tree_sitter.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_treesitter_debug.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "test_treesitter_parsing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_treesitter_debug.test_treesitter_parsing", "name": "test_treesitter_parsing", "type": null}}, "tree_sitter": {".class": "SymbolTableNode", "cross_ref": "tree_sitter", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_treesitter_debug.py"}