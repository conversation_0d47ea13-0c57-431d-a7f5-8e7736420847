{"data_mtime": 1753289443, "dep_lines": [6, 7, 8, 230, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["typing", "dataclasses", "enum", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "b8b901411c6aa72de1320eb905795a35f2e3dff5", "id": "ranking_config", "ignore_all": false, "interface_hash": "60376668779f7365ac3c7f6c5b99460c1beef81a", "mtime": 1753289443, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\ranking_config.py", "plugin_data": null, "size": 11986, "suppressed": [], "version_id": "1.15.0"}