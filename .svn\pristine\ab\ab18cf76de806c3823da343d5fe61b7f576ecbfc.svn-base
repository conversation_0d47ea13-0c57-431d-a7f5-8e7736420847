"""
Test configuration and authentication features.
Tests server configuration, API endpoints, and authentication mechanisms.
"""
import pytest
import httpx
import json


class TestConfiguration:
    """Test configuration and authentication capabilities."""

    @pytest.mark.asyncio
    async def test_server_configuration_completeness(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that server configuration is complete and valid."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Essential configuration fields
        essential_fields = [
            "message",
            "version", 
            "supported_languages",
            "ollama_host",
            "ollama_status"
        ]
        
        missing_fields = [field for field in essential_fields if field not in data]
        assert len(missing_fields) == 0, f"Missing essential configuration fields: {missing_fields}"
        
        print(f"✅ All essential configuration fields present")

    @pytest.mark.asyncio
    async def test_ollama_integration_configuration(self, http_client: httpx.AsyncClient):
        """Test Ollama integration configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Ollama configuration validation
        assert "ollama_host" in data
        ollama_host = data["ollama_host"]
        assert isinstance(ollama_host, str)
        assert "11434" in ollama_host  # Standard Ollama port
        
        assert "ollama_status" in data
        ollama_status = data["ollama_status"]
        assert ollama_status in ["connected", "available", "ready", "online"]
        
        print(f"✅ Ollama host: {ollama_host}")
        print(f"✅ Ollama status: {ollama_status}")

    @pytest.mark.asyncio
    async def test_api_endpoint_accessibility(self, http_client: httpx.AsyncClient):
        """Test that all API endpoints are accessible."""
        # Core endpoints that should be accessible
        endpoints = [
            ("/", "GET"),
            ("/health", "GET"),
            ("/tools/framework_status", "GET"),
            ("/tools/gpu_status", "GET"),
            ("/tools/list_codebases", "POST"),
            ("/tools/detect_intent", "POST")
        ]
        
        accessible_endpoints = []
        
        for endpoint, method in endpoints:
            try:
                if method == "GET":
                    response = await http_client.get(endpoint)
                else:
                    # Use minimal payload for POST requests
                    payload = {"query": "test"} if "detect_intent" in endpoint else {}
                    response = await http_client.post(endpoint, json=payload)
                
                if response.status_code == 200:
                    accessible_endpoints.append(endpoint)
                    print(f"✅ {method} {endpoint} - Accessible")
                else:
                    print(f"⚠️ {method} {endpoint} - Status {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {method} {endpoint} - Error: {e}")
        
        # Should have most endpoints accessible
        assert len(accessible_endpoints) >= 4, f"Too few accessible endpoints: {accessible_endpoints}"

    @pytest.mark.asyncio
    async def test_cors_and_headers_configuration(self, http_client: httpx.AsyncClient):
        """Test CORS and headers configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        # Check response headers
        headers = response.headers
        
        # Should have proper content type
        assert "content-type" in headers
        content_type = headers["content-type"]
        assert "application/json" in content_type.lower()
        
        print(f"✅ Content-Type: {content_type}")

    @pytest.mark.asyncio
    async def test_error_handling_configuration(self, http_client: httpx.AsyncClient):
        """Test error handling configuration."""
        # Test with invalid endpoint
        response = await http_client.get("/nonexistent_endpoint")
        assert response.status_code == 404
        
        # Test with invalid JSON payload
        try:
            response = await http_client.post("/tools/enhanced_search", 
                                            json={"invalid": "payload"})
            # Should handle gracefully (200 with error message or 400)
            assert response.status_code in [200, 400, 422]
            
            if response.status_code == 200:
                data = response.json()
                # Should have error handling in response
                assert "error" in data or "result" in data
                
        except Exception as e:
            print(f"Error handling test exception: {e}")

    @pytest.mark.asyncio
    async def test_timeout_configuration(self, http_client: httpx.AsyncClient):
        """Test timeout configuration for long-running operations."""
        # Test with a potentially long-running query
        payload = {
            "codebase_name": "utils",
            "query": "comprehensive analysis of all functions",
            "max_results": 20
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            # Should either complete or timeout gracefully
            assert response.status_code in [200, 408, 504]  # OK, timeout, or gateway timeout
            
            if response.status_code == 200:
                data = response.json()
                assert "result" in data or "error" in data
                
        except httpx.ReadTimeout:
            # Timeout is acceptable for long-running operations
            print("✅ Timeout handling working correctly")

    @pytest.mark.asyncio
    async def test_json_response_format_consistency(self, http_client: httpx.AsyncClient):
        """Test that JSON response formats are consistent."""
        # Test multiple endpoints for consistent JSON structure
        test_endpoints = [
            ("/", "GET", {}),
            ("/health", "GET", {}),
            ("/tools/list_codebases", "POST", {}),
            ("/tools/detect_intent", "POST", {"query": "test"})
        ]
        
        for endpoint, method, payload in test_endpoints:
            try:
                if method == "GET":
                    response = await http_client.get(endpoint)
                else:
                    response = await http_client.post(endpoint, json=payload)
                
                if response.status_code == 200:
                    # Should return valid JSON
                    data = response.json()
                    assert isinstance(data, dict), f"{endpoint} should return JSON object"
                    
                    print(f"✅ {endpoint} returns valid JSON")
                    
            except json.JSONDecodeError:
                pytest.fail(f"{endpoint} returned invalid JSON")
            except Exception as e:
                print(f"⚠️ {endpoint} test failed: {e}")

    @pytest.mark.asyncio
    async def test_logging_configuration(self, http_client: httpx.AsyncClient):
        """Test logging configuration indicators."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Look for logging-related configuration
        if "version" in data:
            # Server should be providing version info (indicates proper logging setup)
            version = data["version"]
            assert len(version) > 0
            
        # Test that server handles requests properly (indicates logging is working)
        # Make a few requests to see if server remains stable
        for i in range(3):
            test_response = await http_client.get("/health")
            assert test_response.status_code == 200
            
        print("✅ Server stability indicates proper logging configuration")

    @pytest.mark.asyncio
    async def test_environment_configuration(self, http_client: httpx.AsyncClient):
        """Test environment-specific configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should indicate proper environment setup
        environment_indicators = [
            "ollama_host",
            "supported_languages",
            "enhanced_features",
            "gpu_infrastructure"
        ]
        
        found_indicators = [ind for ind in environment_indicators if ind in data]
        
        # Should have most environment indicators
        assert len(found_indicators) >= 3, f"Missing environment indicators: {set(environment_indicators) - set(found_indicators)}"
        
        print(f"✅ Environment indicators: {found_indicators}")

    @pytest.mark.asyncio
    async def test_security_headers(self, http_client: httpx.AsyncClient):
        """Test security-related headers and configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        # Server should respond properly to requests (basic security)
        assert len(response.content) > 0
        
        # Should not expose sensitive information in headers
        headers = response.headers
        sensitive_headers = ["server", "x-powered-by"]
        
        for header in sensitive_headers:
            if header in headers:
                header_value = headers[header].lower()
                # Should not expose detailed server information
                assert "version" not in header_value or "apache" not in header_value
                
        print("✅ No sensitive information exposed in headers")

    @pytest.mark.asyncio
    async def test_rate_limiting_configuration(self, http_client: httpx.AsyncClient):
        """Test rate limiting configuration."""
        # Make multiple rapid requests to test rate limiting
        responses = []
        
        for i in range(5):
            try:
                response = await http_client.get("/health")
                responses.append(response.status_code)
            except Exception as e:
                print(f"Request {i+1} failed: {e}")
        
        # Should handle multiple requests gracefully
        successful_requests = [r for r in responses if r == 200]
        assert len(successful_requests) >= 3, "Server should handle multiple rapid requests"
        
        print(f"✅ Handled {len(successful_requests)}/5 rapid requests successfully")

    @pytest.mark.asyncio
    async def test_content_encoding_configuration(self, http_client: httpx.AsyncClient):
        """Test content encoding configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        # Should return properly encoded content
        data = response.json()
        
        # Test with various character sets in queries
        test_payload = {
            "query": "test with special characters: àáâãäåæçèéêë",
            "codebase_name": "utils"
        }
        
        try:
            response = await http_client.post("/tools/detect_intent", json=test_payload)
            assert response.status_code == 200
            
            # Should handle special characters properly
            result_data = response.json()
            assert isinstance(result_data, dict)
            
            print("✅ Special character encoding handled correctly")
            
        except Exception as e:
            print(f"⚠️ Character encoding test failed: {e}")

    @pytest.mark.asyncio
    async def test_api_versioning_configuration(self, http_client: httpx.AsyncClient):
        """Test API versioning configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have version information
        if "version" in data:
            version = data["version"]
            assert isinstance(version, str)
            assert len(version) > 0
            
            # Should be a reasonable version format
            version_patterns = [".", "v", "3", "2"]
            has_version_pattern = any(pattern in version for pattern in version_patterns)
            assert has_version_pattern, f"Version format seems invalid: {version}"
            
            print(f"✅ API version: {version}")

    @pytest.mark.asyncio
    async def test_service_discovery_configuration(self, http_client: httpx.AsyncClient):
        """Test service discovery configuration."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should provide service discovery information
        service_info = [
            "message",
            "enhanced_features",
            "optimized_tools",
            "management_tools"
        ]
        
        found_info = [info for info in service_info if info in data]
        
        # Should provide comprehensive service information
        assert len(found_info) >= 3, f"Insufficient service discovery info: {found_info}"
        
        print(f"✅ Service discovery info: {found_info}")

    @pytest.mark.asyncio
    async def test_health_check_configuration(self, http_client: httpx.AsyncClient):
        """Test health check configuration."""
        response = await http_client.get("/health")
        assert response.status_code == 200
        
        # Health endpoint should be fast and reliable
        import time
        start_time = time.time()
        
        for i in range(3):
            health_response = await http_client.get("/health")
            assert health_response.status_code == 200
            
        end_time = time.time()
        total_time = end_time - start_time
        
        # Health checks should be fast
        assert total_time < 5.0, f"Health checks too slow: {total_time}s"
        
        print(f"✅ Health check performance: {total_time:.2f}s for 3 requests")
