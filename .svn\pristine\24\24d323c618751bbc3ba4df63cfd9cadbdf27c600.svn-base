# Contributing Guide

Welcome to the OpenWebUI RAG Code Server project! This guide will help you get started with contributing to the project.

## 🎯 How to Contribute

We welcome contributions in many forms:

- **Bug Reports**: Help us identify and fix issues
- **Feature Requests**: Suggest new functionality
- **Code Contributions**: Submit bug fixes and new features
- **Documentation**: Improve guides and documentation
- **Testing**: Help expand test coverage
- **Language Support**: Add new programming language processors

## 🚀 Getting Started

### 1. Development Environment Setup

#### Prerequisites
- Python 3.8+
- Docker and Docker Compose
- Git
- Code editor (VS Code recommended)

#### Clone and Setup
```bash
# Fork the repository on GitHub first, then clone your fork
git clone https://github.com/your-username/openwebui_rag_code_server.git
cd openwebui_rag_code_server

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

#### Development Dependencies
```bash
# Install additional development tools
pip install pytest pytest-cov black flake8 mypy pre-commit

# Setup pre-commit hooks
pre-commit install
```

### 2. Development Workflow

#### Branch Strategy
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Or bug fix branch
git checkout -b fix/issue-description

# Or documentation branch
git checkout -b docs/documentation-improvement
```

#### Code Style
We use several tools to maintain code quality:

```bash
# Format code with Black
black .

# Check code style with flake8
flake8 .

# Type checking with mypy
mypy .

# Run all checks
pre-commit run --all-files
```

#### Testing
```bash
# Run unit tests
python -m pytest unit-tests/

# Run with coverage
python -m pytest unit-tests/ --cov=. --cov-report=html

# Run specific test file
python -m pytest unit-tests/test_language_support.py

# Run integration tests
python run_all_tests.py
```

## 📝 Code Guidelines

### Python Code Style

#### Formatting
- Use **Black** for code formatting
- Line length: 88 characters (Black default)
- Use double quotes for strings
- Follow PEP 8 conventions

#### Type Hints
```python
# Always use type hints
def process_codebase(name: str, exclude_dirs: List[str] = None) -> Dict[str, Any]:
    """Process a codebase with optional directory exclusions."""
    if exclude_dirs is None:
        exclude_dirs = []
    return {"status": "success", "name": name}

# Use proper imports for types
from typing import List, Dict, Any, Optional, Union
```

#### Documentation
```python
def search_code(query: str, codebase: str, max_results: int = 10) -> List[Dict]:
    """
    Search for code snippets in a codebase.
    
    Args:
        query: Search query string
        codebase: Name of the codebase to search
        max_results: Maximum number of results to return
        
    Returns:
        List of search results with metadata
        
    Raises:
        CodebaseNotFoundError: If codebase doesn't exist
        SearchError: If search operation fails
    """
    pass
```

#### Error Handling
```python
# Use specific exception types
class CodebaseNotFoundError(Exception):
    """Raised when a requested codebase is not found."""
    pass

# Proper error handling
try:
    result = process_codebase(name)
except CodebaseNotFoundError as e:
    logger.error(f"Codebase not found: {e}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise ProcessingError(f"Failed to process codebase: {e}")
```

### API Design

#### REST Endpoints
```python
# Use consistent naming
@app.post("/tools/process_codebase")
@app.post("/tools/search_code")
@app.get("/health")

# Proper request/response models
class SearchRequest(BaseModel):
    query: str
    codebase_name: str
    max_results: int = 10
    filter_language: Optional[str] = None

class SearchResponse(BaseModel):
    success: bool
    results: List[SearchResult]
    metadata: Dict[str, Any]
```

#### Error Responses
```python
# Consistent error format
{
    "success": false,
    "error": {
        "code": "CODEBASE_NOT_FOUND",
        "message": "Codebase 'invalid' not found",
        "details": {
            "available_codebases": ["utils", "networking_project"]
        }
    },
    "timestamp": "2025-01-01T12:00:00Z"
}
```

## 🧪 Testing Guidelines

### Unit Tests

#### Test Structure
```python
# test_example.py
import pytest
from unittest.mock import Mock, patch
from your_module import YourClass

class TestYourClass:
    def setup_method(self):
        """Setup for each test method."""
        self.instance = YourClass()
    
    def test_basic_functionality(self):
        """Test basic functionality works correctly."""
        result = self.instance.method("input")
        assert result == "expected_output"
    
    @pytest.mark.asyncio
    async def test_async_method(self):
        """Test async methods."""
        result = await self.instance.async_method()
        assert result is not None
    
    def test_error_handling(self):
        """Test error conditions."""
        with pytest.raises(ValueError):
            self.instance.method("invalid_input")
```

#### Test Coverage
- Aim for 90%+ test coverage
- Test both success and error paths
- Include edge cases and boundary conditions
- Mock external dependencies

#### Integration Tests
```python
# test_integration.py
def test_full_workflow():
    """Test complete codebase processing workflow."""
    # Setup test data
    test_codebase = create_test_codebase()
    
    # Process codebase
    result = process_codebase(test_codebase.name)
    assert result["success"] is True
    
    # Search for code
    search_result = search_code("test function", test_codebase.name)
    assert len(search_result) > 0
    
    # Cleanup
    delete_codebase(test_codebase.name)
```

## 🌍 Adding Language Support

### Creating a New Language Processor

```python
# language_processors.py
class NewLanguageProcessor(LanguageProcessor):
    """Processor for NewLanguage programming language."""
    
    def get_language_name(self) -> str:
        return "newlanguage"
    
    def get_supported_extensions(self) -> Set[str]:
        return {".nl", ".newlang"}
    
    def can_process_file(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in self.get_supported_extensions()
    
    async def extract_functions(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Extract functions from NewLanguage code."""
        # Implement language-specific function extraction
        pass
    
    async def extract_classes(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Extract classes from NewLanguage code."""
        # Implement language-specific class extraction
        pass
```

### Registering the Processor

```python
# language_registry.py
def create_language_registry() -> CodeAnalysisFramework:
    """Create framework with all language processors."""
    framework = CodeAnalysisFramework()
    
    # Existing processors...
    
    # Add new language processor
    framework.register_language_processor(NewLanguageProcessor())
    
    return framework
```

### Adding Tests

```python
# unit-tests/test_new_language.py
class TestNewLanguageProcessor:
    def setup_method(self):
        self.processor = NewLanguageProcessor()
    
    def test_language_detection(self):
        """Test language detection for new language."""
        assert self.processor.can_process_file(Path("test.nl"))
        assert not self.processor.can_process_file(Path("test.py"))
    
    def test_function_extraction(self):
        """Test function extraction from new language code."""
        code = """
        func hello_world() {
            print("Hello, World!")
        }
        """
        functions = await self.processor.extract_functions(code, Path("test.nl"))
        assert len(functions) == 1
        assert functions[0]["name"] == "hello_world"
```

## 📚 Documentation Guidelines

### Code Documentation
- Use docstrings for all public functions and classes
- Include type hints in function signatures
- Document parameters, return values, and exceptions
- Provide usage examples for complex functions

### README and Guides
- Use clear, concise language
- Include code examples
- Provide step-by-step instructions
- Keep documentation up-to-date with code changes

### API Documentation
- Document all endpoints with OpenAPI/Swagger
- Include request/response examples
- Document error conditions
- Provide authentication details

## 🔄 Pull Request Process

### Before Submitting

1. **Run Tests**: Ensure all tests pass
   ```bash
   python -m pytest unit-tests/
   python run_all_tests.py
   ```

2. **Code Quality**: Run linting and formatting
   ```bash
   black .
   flake8 .
   mypy .
   ```

3. **Documentation**: Update relevant documentation
4. **Changelog**: Add entry to CHANGELOG.md if applicable

### Pull Request Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs automatically
2. **Code Review**: Maintainers review code and provide feedback
3. **Testing**: Additional testing may be performed
4. **Approval**: At least one maintainer approval required
5. **Merge**: Squash and merge to main branch

## 🐛 Bug Reports

### Bug Report Template

```markdown
## Bug Description
Clear description of the bug

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., Ubuntu 20.04]
- Python version: [e.g., 3.9.7]
- Docker version: [e.g., 20.10.8]
- Server version: [e.g., 3.0.0]

## Additional Context
Any other relevant information
```

## 💡 Feature Requests

### Feature Request Template

```markdown
## Feature Description
Clear description of the proposed feature

## Use Case
Why is this feature needed?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other approaches that were considered

## Additional Context
Any other relevant information
```

## 🏆 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- GitHub contributor statistics

## 📞 Getting Help

- **Documentation**: Check existing documentation first
- **Discussions**: Use GitHub Discussions for questions
- **Issues**: Create issues for bugs and feature requests
- **Discord/Slack**: Join community channels (if available)

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).

Thank you for contributing to the OpenWebUI RAG Code Server project! 🎉
