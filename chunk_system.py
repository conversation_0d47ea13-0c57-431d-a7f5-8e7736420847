"""
Extensible Chunk Type System
Implements Phase 0, Section 3 from TODO_SOFTWARE.md
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class ChunkPriority(Enum):
    """Priority levels for chunk processing"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ChunkMetadata:
    """Metadata associated with a chunk"""
    chunk_id: str
    chunk_type: str
    source_files: List[str]
    language: str
    created_at: datetime
    priority: ChunkPriority
    tags: Set[str]
    relationships: Dict[str, List[str]]
    quality_score: float
    processing_time: float
    custom_attributes: Dict[str, Any]

@dataclass
class ChunkContent:
    """Content of a chunk"""
    primary_content: str
    secondary_content: Optional[str] = None
    summary: Optional[str] = None
    keywords: Optional[List[str]] = None
    embeddings: Optional[List[float]] = None

@dataclass
class Chunk:
    """Complete chunk with content and metadata"""
    content: ChunkContent
    metadata: ChunkMetadata
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary for storage"""
        return {
            "content": {
                "primary_content": self.content.primary_content,
                "secondary_content": self.content.secondary_content,
                "summary": self.content.summary,
                "keywords": self.content.keywords or [],
                "embeddings": self.content.embeddings
            },
            "metadata": {
                "chunk_id": self.metadata.chunk_id,
                "chunk_type": self.metadata.chunk_type,
                "source_files": self.metadata.source_files,
                "language": self.metadata.language,
                "created_at": self.metadata.created_at.isoformat(),
                "priority": self.metadata.priority.value,
                "tags": list(self.metadata.tags),
                "relationships": self.metadata.relationships,
                "quality_score": self.metadata.quality_score,
                "processing_time": self.metadata.processing_time,
                "custom_attributes": self.metadata.custom_attributes
            }
        }

class ChunkType(ABC):
    """Abstract base for extensible chunk types"""
    
    @abstractmethod
    async def generate(self, context: Dict[str, Any], llm_client: Any) -> Chunk:
        """Generate chunk content from context"""
        pass
    
    @abstractmethod
    def get_metadata_template(self) -> Dict[str, Any]:
        """Return chunk metadata template"""
        pass
    
    @abstractmethod
    def get_chunk_type_name(self) -> str:
        """Return unique chunk type identifier"""
        pass
    
    def get_chunk_description(self) -> str:
        """Return human-readable description"""
        return f"Chunk type: {self.get_chunk_type_name()}"
    
    def get_priority(self) -> ChunkPriority:
        """Return default priority for this chunk type"""
        return ChunkPriority.MEDIUM
    
    def get_required_context_keys(self) -> List[str]:
        """Return required keys in context for generation"""
        return []
    
    def validate_context(self, context: Dict[str, Any]) -> bool:
        """Validate context before chunk generation"""
        required_keys = self.get_required_context_keys()
        return all(key in context for key in required_keys)
    
    def estimate_processing_time(self, context: Dict[str, Any]) -> float:
        """Estimate processing time in seconds"""
        return 1.0  # Default estimate
    
    def get_default_tags(self) -> Set[str]:
        """Return default tags for this chunk type"""
        return {self.get_chunk_type_name()}

class ChunkGeneratorRegistry:
    """Registry for extensible chunk generators"""
    
    def __init__(self):
        self.generators: Dict[str, ChunkType] = {}
        self.generation_stats: Dict[str, Dict[str, Any]] = {}
        
    def register_chunk_type(self, chunk_type: ChunkType) -> None:
        """Register a new chunk type generator"""
        type_name = chunk_type.get_chunk_type_name()
        
        if type_name in self.generators:
            logger.warning(f"Chunk type {type_name} already registered, replacing...")
        
        self.generators[type_name] = chunk_type
        self.generation_stats[type_name] = {
            "total_generated": 0,
            "total_time": 0.0,
            "average_time": 0.0,
            "success_rate": 0.0,
            "last_generated": None
        }
        
        logger.info(f"Registered chunk type: {type_name}")
    
    def unregister_chunk_type(self, type_name: str) -> bool:
        """Unregister a chunk type"""
        if type_name not in self.generators:
            return False
        
        del self.generators[type_name]
        del self.generation_stats[type_name]
        
        logger.info(f"Unregistered chunk type: {type_name}")
        return True
    
    def get_registered_types(self) -> List[str]:
        """Get list of registered chunk types"""
        return list(self.generators.keys())
    
    def get_chunk_generator(self, type_name: str) -> Optional[ChunkType]:
        """Get chunk generator by type name"""
        return self.generators.get(type_name)
    
    async def generate_chunk(self, chunk_type_name: str, context: Dict[str, Any], llm_client: Any) -> Chunk:
        """Generate chunk of specified type"""
        import time
        import uuid
        
        if chunk_type_name not in self.generators:
            raise ValueError(f"Unknown chunk type: {chunk_type_name}")
        
        generator = self.generators[chunk_type_name]
        
        # Validate context
        if not generator.validate_context(context):
            raise ValueError(f"Invalid context for chunk type {chunk_type_name}")
        
        start_time = time.time()
        
        try:
            # Generate chunk
            chunk = await generator.generate(context, llm_client)
            
            # Update chunk metadata
            processing_time = time.time() - start_time
            chunk.metadata.processing_time = processing_time
            chunk.metadata.created_at = datetime.now()
            
            if not chunk.metadata.chunk_id:
                chunk.metadata.chunk_id = str(uuid.uuid4())
            
            # Update statistics
            stats = self.generation_stats[chunk_type_name]
            stats["total_generated"] += 1
            stats["total_time"] += processing_time
            stats["average_time"] = stats["total_time"] / stats["total_generated"]
            stats["last_generated"] = datetime.now().isoformat()
            
            logger.info(f"Generated chunk {chunk.metadata.chunk_id} of type {chunk_type_name} in {processing_time:.2f}s")
            
            return chunk
        
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Failed to generate chunk of type {chunk_type_name} after {processing_time:.2f}s: {e}")
            raise
    
    async def generate_multiple_chunks(self, chunk_requests: List[Dict[str, Any]], llm_client: Any) -> List[Chunk]:
        """Generate multiple chunks in batch"""
        import asyncio
        
        tasks = []
        for request in chunk_requests:
            chunk_type = request["chunk_type"]
            context = request["context"]
            
            task = self.generate_chunk(chunk_type, context, llm_client)
            tasks.append(task)
        
        # Execute all chunk generation tasks
        chunks = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log errors
        successful_chunks = []
        for i, result in enumerate(chunks):
            if isinstance(result, Exception):
                logger.error(f"Chunk generation {i} failed: {result}")
            else:
                successful_chunks.append(result)
        
        return successful_chunks
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get chunk generation statistics"""
        return {
            "total_types": len(self.generators),
            "type_statistics": self.generation_stats.copy(),
            "overall_stats": {
                "total_chunks": sum(stats["total_generated"] for stats in self.generation_stats.values()),
                "total_time": sum(stats["total_time"] for stats in self.generation_stats.values()),
                "average_time_per_chunk": sum(stats["average_time"] for stats in self.generation_stats.values()) / len(self.generation_stats) if self.generation_stats else 0
            }
        }
    
    def get_registry_info(self) -> Dict[str, Any]:
        """Get information about the chunk registry"""
        return {
            "registered_types": list(self.generators.keys()),
            "type_details": {
                name: {
                    "description": generator.get_chunk_description(),
                    "priority": generator.get_priority().name,
                    "required_context": generator.get_required_context_keys(),
                    "default_tags": list(generator.get_default_tags())
                }
                for name, generator in self.generators.items()
            },
            "statistics": self.get_generation_statistics()
        }

# Core chunk type implementations

class CodeImplementationChunk(ChunkType):
    """Chunk type for code implementation analysis"""
    
    def get_chunk_type_name(self) -> str:
        return "code_implementation"
    
    def get_priority(self) -> ChunkPriority:
        return ChunkPriority.HIGH
    
    def get_required_context_keys(self) -> List[str]:
        return ["code_content", "file_path", "language"]
    
    async def generate(self, context: Dict[str, Any], llm_client: Any) -> Chunk:
        """Generate code implementation chunk with Tree-sitter metadata support"""
        import uuid

        code_content = context["code_content"]
        file_path = context["file_path"]
        language = context["language"]

        # Extract Tree-sitter metadata if available
        start_line = context.get("start_line", 1)
        end_line = context.get("end_line", len(code_content.splitlines()))
        function_name = context.get("function_name", "")
        chunk_type = context.get("chunk_type", "code_implementation")

        # Create chunk content
        content = ChunkContent(
            primary_content=code_content,
            summary=f"Code implementation from {file_path}" + (f" - {function_name}" if function_name else ""),
            keywords=self._extract_keywords(code_content)
        )

        # Create metadata with Tree-sitter information
        custom_attributes = {
            "file_path": file_path,
            "language": language,
            "content_length": len(code_content),
            "line_count": len(code_content.splitlines()),
            "start_line": start_line,
            "end_line": end_line
        }

        # Add function name if available
        if function_name:
            custom_attributes["function_name"] = function_name
            custom_attributes["chunk_type"] = chunk_type

        # Add semantic enhancement attributes if available
        if "semantic_enhanced" in context:
            custom_attributes["semantic_enhanced"] = context["semantic_enhanced"]
        if "context_level" in context:
            custom_attributes["context_level"] = context["context_level"]
        if "semantic_tags" in context:
            custom_attributes["semantic_tags"] = context["semantic_tags"]
        if "dependencies" in context:
            custom_attributes["dependencies"] = context["dependencies"]
        if "documentation" in context:
            custom_attributes["documentation"] = context["documentation"]
        if "element_type" in context:
            custom_attributes["element_type"] = context["element_type"]

        metadata = ChunkMetadata(
            chunk_id=str(uuid.uuid4()),
            chunk_type=self.get_chunk_type_name(),
            source_files=[file_path],
            language=language,
            created_at=datetime.now(),
            priority=self.get_priority(),
            tags=self.get_default_tags() | ({"function"} if function_name else set()),
            relationships={},
            quality_score=self._calculate_quality_score(code_content),
            processing_time=0.0,
            custom_attributes=custom_attributes
        )

        return Chunk(content=content, metadata=metadata)
    
    def get_metadata_template(self) -> Dict[str, Any]:
        """Return metadata template"""
        return {
            "chunk_type": self.get_chunk_type_name(),
            "priority": self.get_priority().value,
            "tags": list(self.get_default_tags()),
            "relationships": {},
            "custom_attributes": {}
        }
    
    def _extract_keywords(self, code_content: str) -> List[str]:
        """Extract keywords from code content"""
        # Simplified keyword extraction
        import re
        
        # Common programming keywords
        keywords = re.findall(r'\b(?:function|class|method|variable|const|let|var|def|async|await|return|if|else|for|while|try|catch)\b', code_content.lower())
        return list(set(keywords))
    
    def _calculate_quality_score(self, code_content: str) -> float:
        """Calculate quality score for code content"""
        # Simplified quality scoring
        lines = code_content.splitlines()
        non_empty_lines = [line for line in lines if line.strip()]
        
        if not non_empty_lines:
            return 0.0
        
        # Basic quality indicators
        has_comments = any('//' in line or '#' in line or '/*' in line for line in lines)
        has_functions = any('def ' in line or 'function ' in line for line in lines)
        avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines)
        
        score = 0.5  # Base score
        if has_comments:
            score += 0.2
        if has_functions:
            score += 0.2
        if 20 <= avg_line_length <= 80:  # Reasonable line length
            score += 0.1
        
        return min(score, 1.0)

class ArchitecturalPatternChunk(ChunkType):
    """Chunk type for architectural pattern analysis"""
    
    def get_chunk_type_name(self) -> str:
        return "architectural_pattern"
    
    def get_priority(self) -> ChunkPriority:
        return ChunkPriority.HIGH
    
    def get_required_context_keys(self) -> List[str]:
        return ["architectural_analysis", "source_files"]
    
    async def generate(self, context: Dict[str, Any], llm_client: Any) -> Chunk:
        """Generate architectural pattern chunk"""
        import uuid
        
        analysis = context["architectural_analysis"]
        source_files = context["source_files"]
        
        # Create chunk content
        content = ChunkContent(
            primary_content=str(analysis),
            summary="Architectural pattern analysis",
            keywords=["architecture", "pattern", "design"]
        )
        
        # Create metadata
        metadata = ChunkMetadata(
            chunk_id=str(uuid.uuid4()),
            chunk_type=self.get_chunk_type_name(),
            source_files=source_files,
            language="multi",
            created_at=datetime.now(),
            priority=self.get_priority(),
            tags=self.get_default_tags() | {"architecture", "design"},
            relationships={},
            quality_score=0.8,  # High quality for architectural analysis
            processing_time=0.0,
            custom_attributes={"analysis_type": "architectural"}
        )
        
        return Chunk(content=content, metadata=metadata)
    
    def get_metadata_template(self) -> Dict[str, Any]:
        """Return metadata template"""
        return {
            "chunk_type": self.get_chunk_type_name(),
            "priority": self.get_priority().value,
            "tags": list(self.get_default_tags() | {"architecture", "design"}),
            "relationships": {},
            "custom_attributes": {"analysis_type": "architectural"}
        }

class SystemDesignChunk(ChunkType):
    """Chunk type for system-level design analysis"""
    
    def get_chunk_type_name(self) -> str:
        return "system_design"
    
    def get_priority(self) -> ChunkPriority:
        return ChunkPriority.CRITICAL
    
    def get_required_context_keys(self) -> List[str]:
        return ["system_analysis", "codebase_files"]
    
    async def generate(self, context: Dict[str, Any], llm_client: Any) -> Chunk:
        """Generate system design chunk"""
        import uuid
        
        analysis = context["system_analysis"]
        codebase_files = context["codebase_files"]
        
        # Create chunk content
        content = ChunkContent(
            primary_content=str(analysis),
            summary="System-level design analysis",
            keywords=["system", "design", "architecture", "overview"]
        )
        
        # Create metadata
        metadata = ChunkMetadata(
            chunk_id=str(uuid.uuid4()),
            chunk_type=self.get_chunk_type_name(),
            source_files=codebase_files,
            language="system",
            created_at=datetime.now(),
            priority=self.get_priority(),
            tags=self.get_default_tags() | {"system", "overview", "design"},
            relationships={},
            quality_score=0.9,  # Very high quality for system analysis
            processing_time=0.0,
            custom_attributes={"analysis_scope": "system_wide"}
        )
        
        return Chunk(content=content, metadata=metadata)
    
    def get_metadata_template(self) -> Dict[str, Any]:
        """Return metadata template"""
        return {
            "chunk_type": self.get_chunk_type_name(),
            "priority": self.get_priority().value,
            "tags": list(self.get_default_tags() | {"system", "overview", "design"}),
            "relationships": {},
            "custom_attributes": {"analysis_scope": "system_wide"}
        }

def create_default_chunk_registry() -> ChunkGeneratorRegistry:
    """Create chunk registry with default chunk types"""
    registry = ChunkGeneratorRegistry()

    # Register core chunk types
    registry.register_chunk_type(CodeImplementationChunk())
    registry.register_chunk_type(ArchitecturalPatternChunk())
    registry.register_chunk_type(SystemDesignChunk())

    logger.info("Created default chunk registry with core chunk types")
    return registry

if __name__ == "__main__":
    # Test the chunk system
    logging.basicConfig(level=logging.INFO)

    print("Creating chunk registry...")
    registry = create_default_chunk_registry()

    print(f"Registered chunk types: {registry.get_registered_types()}")

    # Test chunk generation
    import asyncio

    async def test_chunk_generation():
        # Mock LLM client
        class MockLLMClient:
            async def generate(self, prompt):
                return "Mock LLM response"

        llm_client = MockLLMClient()

        # Test code implementation chunk
        context = {
            "code_content": "def hello_world():\n    print('Hello, World!')",
            "file_path": "test.py",
            "language": "python"
        }

        chunk = await registry.generate_chunk("code_implementation", context, llm_client)
        print(f"Generated chunk: {chunk.metadata.chunk_id}")
        print(f"Chunk type: {chunk.metadata.chunk_type}")
        print(f"Quality score: {chunk.metadata.quality_score}")

    asyncio.run(test_chunk_generation())
