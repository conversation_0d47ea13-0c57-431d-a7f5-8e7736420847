@echo off
REM Sync all essential files to home-ai-server using SCP
REM Run this script from the local repository root
REM Requires SSH/SCP tools (like Git Bash, WSL, or PuTTY)

set REMOTE_HOST=home-ai-server
set REMOTE_USER=fvaneijk
set REMOTE_PATH=/home/<USER>/home-ai-system/code_analyzer_server
set LOCAL_PATH=.

echo 🚀 Syncing OpenWebUI RAG Code Server to %REMOTE_HOST%
echo ============================================================
echo Local:  %LOCAL_PATH%
echo Remote: %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%
echo.

echo 📁 Ensuring remote directory exists...
ssh %REMOTE_USER%@%REMOTE_HOST% "mkdir -p %REMOTE_PATH%"

echo 📤 Syncing core application files...
scp main.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp framework_integration.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp language_framework.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp language_processors.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp language_registry.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp gpu_infrastructure.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp processing_pipeline.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp chunk_system.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp vector_db_creator.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp open_webui_code_analyzer_tool.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp intent_detection_service.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp semantic_patterns.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp metta_processor.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp tree_sitter_chunker.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp embedding_config.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp code_preprocessor.py %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo ⚙️ Syncing configuration files...
scp requirements.txt %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp docker-compose.yml %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp Dockerfile %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp pytest.ini %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 📋 Syncing config directory...
scp -r config %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 🧪 Syncing unit test framework...
scp -r unit-tests %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 🔧 Syncing execution scripts...
scp run_unit_tests.bat %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
scp run_unit_tests.sh %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 📝 Copying remote verification script...
scp check_remote_files.sh %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/

echo 🔐 Making scripts executable...
ssh %REMOTE_USER%@%REMOTE_HOST% "chmod +x %REMOTE_PATH%/run_unit_tests.sh %REMOTE_PATH%/check_remote_files.sh"

echo.
echo ✅ Sync completed!
echo.
echo 🔍 Next steps:
echo 1. SSH to the server: ssh %REMOTE_USER%@%REMOTE_HOST%
echo 2. Navigate to: cd %REMOTE_PATH%
echo 3. Verify files: bash check_remote_files.sh
echo 4. Test the system: bash run_unit_tests.sh
echo 5. Start the server: docker-compose up -d
echo.
echo 📊 Files synced:
echo    - Core application files (16 files)
echo    - Configuration files (4 files)
echo    - Unit test framework (15 test modules)
echo    - Execution scripts (2 files)
echo    - Verification script (1 file)
echo.
echo 🎯 Your OpenWebUI RAG Code Server is ready for deployment!

pause
