"""
Test programming language API endpoints.
Tests the comprehensive language information endpoints and language detection capabilities.
"""
import pytest
import httpx
from typing import Dict, List, Any


@pytest.mark.framework
@pytest.mark.language
@pytest.mark.analysis
class TestLanguageEndpoints:
    """Test programming language API endpoints and functionality."""

    @pytest.mark.asyncio
    async def test_get_all_languages_info(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages endpoint for comprehensive language information."""
        response = await http_client.get("/api/languages")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "total_languages" in data
        assert "total_extensions" in data
        assert "languages" in data
        assert "categories" in data
        assert "processor_types" in data
        
        # Verify we have a reasonable number of languages (45+)
        assert data["total_languages"] >= 45
        assert data["total_extensions"] >= 50
        
        # Verify languages structure
        languages = data["languages"]
        assert isinstance(languages, dict)
        assert len(languages) >= 45
        
        # Check a few core languages exist
        core_languages = ["python", "c_cpp", "javascript", "csharp"]
        for lang in core_languages:
            assert lang in languages, f"Core language {lang} not found"
            
            lang_info = languages[lang]
            assert "name" in lang_info
            assert "extensions" in lang_info
            assert "processor_type" in lang_info
            assert "priority" in lang_info
            assert "category" in lang_info
            assert "tree_sitter_support" in lang_info
            assert "description" in lang_info

    @pytest.mark.asyncio
    async def test_get_languages_list(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages/list endpoint for simple language list."""
        response = await http_client.get("/api/languages/list")
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "languages" in data
        assert "total" in data
        
        # Verify we have expected number of languages
        assert data["total"] >= 45
        assert len(data["languages"]) >= 45
        
        # Verify languages are strings
        languages = data["languages"]
        assert isinstance(languages, list)
        assert all(isinstance(lang, str) for lang in languages)
        
        # Check core languages are present
        core_languages = ["python", "c_cpp", "javascript", "csharp"]
        for lang in core_languages:
            assert lang in languages, f"Core language {lang} not in list"

    @pytest.mark.asyncio
    async def test_get_specific_language_details(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages/{language_name} endpoint."""
        test_languages = ["python", "c_cpp", "javascript", "csharp", "java", "rust"]
        
        for language in test_languages:
            response = await http_client.get(f"/api/languages/{language}")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify language details structure
            assert "name" in data
            assert "extensions" in data
            assert "processor_type" in data
            assert "priority" in data
            assert "category" in data
            assert "tree_sitter_support" in data
            assert "description" in data
            
            # Verify data types
            assert data["name"] == language
            assert isinstance(data["extensions"], list)
            assert data["processor_type"] in ["specialized", "generic"]
            assert isinstance(data["priority"], int)
            assert isinstance(data["category"], str)
            assert isinstance(data["tree_sitter_support"], bool)
            assert isinstance(data["description"], str)
            
            # Verify extensions are valid
            assert len(data["extensions"]) > 0
            for ext in data["extensions"]:
                assert ext.startswith("."), f"Extension {ext} should start with dot"

    @pytest.mark.asyncio
    async def test_get_nonexistent_language(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages/{language_name} with non-existent language."""
        response = await http_client.get("/api/languages/nonexistent_language")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "error" in data
        assert "not supported" in data["error"].lower()

    @pytest.mark.asyncio
    async def test_get_languages_by_category(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages/category/{category} endpoint."""
        # First get all categories
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        all_data = response.json()
        
        categories = all_data["categories"]
        assert isinstance(categories, dict)
        
        # Test a few known categories
        test_categories = []
        for category in categories.keys():
            test_categories.append(category)
            if len(test_categories) >= 5:  # Test first 5 categories
                break
        
        for category in test_categories:
            response = await http_client.get(f"/api/languages/category/{category}")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify category response structure
            assert "count" in data
            assert "languages" in data
            
            # Verify data consistency
            assert isinstance(data["count"], int)
            assert isinstance(data["languages"], list)
            assert data["count"] == len(data["languages"])
            assert data["count"] > 0  # Each category should have at least one language

    @pytest.mark.asyncio
    async def test_get_nonexistent_category(self, http_client: httpx.AsyncClient, server_health_check):
        """Test GET /api/languages/category/{category} with non-existent category."""
        response = await http_client.get("/api/languages/category/nonexistent_category")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "error" in data
        assert "available_categories" in data
        assert isinstance(data["available_categories"], list)

    @pytest.mark.asyncio
    async def test_language_categories_structure(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that language categories are properly structured."""
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        data = response.json()
        
        categories = data["categories"]
        expected_categories = [
            "systems_programming",
            "web_technologies", 
            "scripting_languages",
            "jvm_languages",
            "functional_languages",
            "ai_logic_programming",
            "data_languages",
            "scientific_computing",
            "build_systems",
            "documentation",
            "hardware_description",
            "assembly",
            "other"
        ]
        
        # Verify expected categories exist
        for category in expected_categories:
            if category in categories:  # Some categories might be empty
                category_data = categories[category]
                assert "count" in category_data
                assert "languages" in category_data
                assert isinstance(category_data["count"], int)
                assert isinstance(category_data["languages"], list)

    @pytest.mark.asyncio
    async def test_processor_types_distribution(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processor types distribution in language information."""
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        data = response.json()
        
        processor_types = data["processor_types"]
        assert "specialized" in processor_types
        assert "generic" in processor_types
        
        # Verify we have both specialized and generic processors
        assert processor_types["specialized"] > 0
        assert processor_types["generic"] > 0
        
        # Total should match total languages
        total_processors = processor_types["specialized"] + processor_types["generic"]
        assert total_processors == data["total_languages"]

    @pytest.mark.asyncio
    async def test_language_extensions_mapping(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that language extensions are properly mapped."""
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        data = response.json()
        
        languages = data["languages"]
        
        # Test specific language extension mappings
        extension_tests = {
            "python": [".py"],
            "c_cpp": [".c", ".cpp", ".h", ".hpp"],
            "javascript": [".js"],
            "csharp": [".cs"],
            "java": [".java"],
            "rust": [".rs"],
            "go": [".go"]
        }
        
        for lang, expected_extensions in extension_tests.items():
            if lang in languages:
                lang_extensions = languages[lang]["extensions"]
                for ext in expected_extensions:
                    assert ext in lang_extensions, f"Extension {ext} not found for {lang}"

    @pytest.mark.asyncio
    async def test_tree_sitter_support_flags(self, http_client: httpx.AsyncClient, server_health_check):
        """Test Tree-sitter support flags in language information."""
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        data = response.json()
        
        languages = data["languages"]
        
        # Count languages with Tree-sitter support
        tree_sitter_count = 0
        for lang_info in languages.values():
            assert "tree_sitter_support" in lang_info
            assert isinstance(lang_info["tree_sitter_support"], bool)
            if lang_info["tree_sitter_support"]:
                tree_sitter_count += 1
        
        # We should have a reasonable number of languages with Tree-sitter support
        assert tree_sitter_count >= 20, f"Only {tree_sitter_count} languages have Tree-sitter support"

    @pytest.mark.asyncio
    async def test_language_descriptions(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that languages have meaningful descriptions."""
        response = await http_client.get("/api/languages")
        assert response.status_code == 200
        data = response.json()
        
        languages = data["languages"]
        
        # Test that core languages have proper descriptions
        core_languages = ["python", "c_cpp", "javascript", "csharp"]
        for lang in core_languages:
            if lang in languages:
                description = languages[lang]["description"]
                assert isinstance(description, str)
                assert len(description) > 10  # Should be meaningful description
                assert lang.replace("_", "").lower() in description.lower() or \
                       any(term in description.lower() for term in ["programming", "language", "script"])

    @pytest.mark.asyncio
    async def test_language_endpoint_performance(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that language endpoints respond within reasonable time."""
        import time
        
        endpoints = [
            "/api/languages",
            "/api/languages/list",
            "/api/languages/python",
            "/api/languages/category/systems_programming"
        ]
        
        for endpoint in endpoints:
            start_time = time.time()
            response = await http_client.get(endpoint)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Language endpoints should be fast (< 2 seconds)
            assert response_time < 2.0, f"Endpoint {endpoint} took {response_time:.2f} seconds"
            
            if endpoint != "/api/languages/category/systems_programming":  # This might 404 if category doesn't exist
                assert response.status_code == 200
