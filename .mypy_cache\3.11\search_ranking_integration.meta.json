{"data_mtime": 1753287309, "dep_lines": [71, 7, 8, 9, 70, 184, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.responses", "logging", "typing", "generic_search_ranking", "<PERSON><PERSON><PERSON>", "re", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "pydantic", "pydantic.networks", "starlette", "starlette.background", "starlette.responses", "types", "typing_extensions"], "hash": "25a5c50ed4686edf4fc275bad3471454439f8a2c", "id": "search_ranking_integration", "ignore_all": false, "interface_hash": "6250c9caa9d81e477689ecbe9c2870a9200900a5", "mtime": 1753289630, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\search_ranking_integration.py", "plugin_data": null, "size": 12106, "suppressed": [], "version_id": "1.15.0"}