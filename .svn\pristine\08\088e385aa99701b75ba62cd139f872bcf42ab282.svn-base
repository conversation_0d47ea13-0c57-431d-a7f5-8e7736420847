# OpenWebUI Integration Guide

Complete guide for installing and using the OpenWebUI Code Analyzer Tool, enabling natural language code analysis through chat interface.

## 🎯 Overview

The OpenWebUI Code Analyzer Tool provides a natural language interface to the RAG Code Server, allowing users to:

- Search code using natural language queries
- Get AI-powered code explanations
- Manage multiple codebases through chat
- Access all server features through conversational interface

## 📋 Prerequisites

### OpenWebUI Setup
- OpenWebUI instance running (version 0.4.0+)
- Admin access to install tools
- Compatible AI model (Llama 3, GPT-4, etc.)

### Code Analyzer Server
- RAG Code Server running and accessible
- Network connectivity between OpenWebUI and Code Analyzer
- API endpoints responding to health checks

## 🔧 Tool Installation

### Step 1: Access OpenWebUI Admin Interface

1. Open your OpenWebUI instance in a web browser
2. Log in with admin credentials
3. Navigate to **Workspace** → **Tools**

### Step 2: Install the Tool

#### Option A: File Upload Installation
1. Download `open_webui_code_analyzer_tool.py` from the repository
2. In OpenWebUI Tools section, click **"Import Tool"**
3. Select the downloaded file
4. Click **"Install"**

#### Option B: Manual Installation
1. Click **"+"** or **"Add Tool"** button
2. Choose **"Create New Tool"**
3. Copy the entire contents of `open_webui_code_analyzer_tool.py`
4. Paste into the tool editor
5. Click **"Save"** or **"Install"**

### Step 3: Configure Tool Settings

After installation, configure the tool valves:

```python
class Valves:
    code_analyzer_server_url: str = "http://your-server:5002"
    enable_remote_ollama: bool = True
    lynn_pc_ollama_url: str = "http://************:11434"
    t5810_ollama_url: str = "http://************:11434"
    request_timeout: int = 90
    ai_timeout: int = 180
```

**Important Configuration Values:**
- `code_analyzer_server_url`: URL of your Code Analyzer Server
- `enable_remote_ollama`: Enable direct Ollama tool calling for remote models
- `request_timeout`: Request timeout in seconds for general operations
- `ai_timeout`: Request timeout in seconds for AI operations

### Step 4: Enable Tool for Models

1. Go to **Workspace** → **Models**
2. Find your target model (e.g., **llama3:latest**)
3. Click the **edit** (✏️) icon
4. Scroll to **"Tools"** section
5. Check the box for **"Code Analysis Tool"** or **"code_analyzer_tool"**
6. Click **"Save"**

## ✅ Verification and Testing

### Basic Functionality Test

Start a new chat with your configured model and try these commands:

```bash
# Test server connectivity
"status"

# List available codebases
"list codebases"

# Get help
"help"
```

**Expected Responses:**
- Status should show server online with version information
- List codebases should show available codebases in your source_code directory
- Help should display available commands and usage examples

### Advanced Testing

```bash
# Process a codebase (if you have source code available)
"process codebase test_project"

# Select a codebase
"select codebase test_project"

# Search for code
"search code memory allocation"

# Ask AI questions
"ask about code: How does error handling work?"
```

## 🎮 Usage Guide

### Basic Commands

#### Codebase Management
```bash
# List all available codebases
"list codebases"
"show me all codebases"
"what codebases are available?"

# Process/index a new codebase
"process codebase my_project"
"index the utils codebase"
"analyze codebase networking_project"

# Select active codebase
"select codebase utils"
"use the z80emu codebase"
"switch to modbus codebase"

# Get codebase statistics
"get stats for utils"
"show statistics for my_project"
"codebase info for networking_project"

# Delete a codebase index
"delete codebase old_project"
"remove the test_project codebase"
```

#### Code Search
```bash
# Basic search
"search code memory allocation"
"find functions related to database"
"look for error handling code"

# Language-specific search
"search code async functions in python"
"find C++ template code"
"search for C# LINQ queries"
"look for JavaScript promises"

# Advanced search with parameters
"search code authentication with 15 results"
"find database code in python with verbose output"
"search for memory management in cpp files"
```

#### AI-Powered Analysis
```bash
# General code questions
"ask about code: How does the authentication system work?"
"explain the database connection logic"
"what design patterns are used in this codebase?"

# Architecture analysis
"ask about code: What is the overall system architecture?"
"analyze the code structure and organization"
"explain the main components and their relationships"

# Performance and quality analysis
"ask about code: What are potential performance issues?"
"identify security vulnerabilities in the code"
"suggest improvements for code quality"
```

#### System Information
```bash
# Server status
"status"
"server status"
"health check"

# Detailed system information
"get server status"
"show system information"
"display server metrics"

# Help and documentation
"help"
"show available commands"
"what can you do?"
```

### Advanced Features

#### Batch Operations
```bash
# Process multiple codebases
"process codebases utils, networking_project, z80emu"

# Compare across codebases
"compare error handling across all codebases"
"find similar patterns in utils and modbus"
```

#### Filtering and Refinement
```bash
# File type filtering
"search code database in .py files"
"find templates in .cpp files"
"look for configs in .json files"

# Category-based search
"search code security patterns"
"find API endpoint definitions"
"look for performance optimizations"
```

#### Verbose and Debug Output
```bash
# Enable verbose output for detailed information
"search code memory with verbose output"
"get detailed stats for utils"

# Debug mode for troubleshooting
"debug search code authentication"
"debug status check"
```

## 🔧 Configuration Options

### Tool Valves Configuration

The tool provides several configuration options through valves:

```python
class Valves:
    # Server Configuration
    code_analyzer_server_url: str = "http://localhost:5002"
    enable_remote_ollama: bool = True

    # Remote Ollama URLs
    lynn_pc_ollama_url: str = "http://************:11434"
    t5810_ollama_url: str = "http://************:11434"

    # Timeout Settings
    request_timeout: int = 90
    ai_timeout: int = 180

    # Feature Toggles
    enable_debug_mode: bool = False
    enable_verbose_output: bool = True
    enable_auto_codebase_detection: bool = True

    # Display Options
    show_metadata: bool = True
    show_performance_metrics: bool = True
    compact_output: bool = False
```

### Environment-Specific Configuration

#### Development Environment
```python
code_analyzer_server_url = "http://localhost:5002"
enable_debug_mode = True
request_timeout = 60
```

#### Production Environment
```python
code_analyzer_server_url = "https://api.your-domain.com"
enable_debug_mode = False
request_timeout = 300
ai_timeout = 180
```

#### Remote Server Configuration
```python
code_analyzer_server_url = "http://home-ai-server.local:5002"
request_timeout = 120
ai_timeout = 240
enable_remote_ollama = True
```

## 🚨 Troubleshooting

### Common Installation Issues

#### Tool Not Appearing in List
**Problem**: Tool doesn't appear after installation
**Solutions**:
1. Check Python syntax in the tool code
2. Verify the tool class is named `Tools`
3. Ensure the docstring header has required fields
4. Refresh the browser and check again

#### Tool Installation Fails
**Problem**: Error during tool installation
**Solutions**:
1. Check OpenWebUI version compatibility (0.4.0+)
2. Verify admin permissions
3. Check browser console for JavaScript errors
4. Try manual installation instead of file upload

### Connection Issues

#### Server Unreachable
**Problem**: "Connection refused" or "Server may be down"
**Solutions**:
1. Verify Code Analyzer Server is running: `curl http://localhost:5002/health`
2. Check network connectivity between OpenWebUI and server
3. Verify firewall settings and port accessibility
4. Update `code_analyzer_server_url` in tool configuration

#### API Authentication Errors
**Problem**: "Unauthorized" or "Invalid API key"
**Solutions**:
1. Check if API key is required on the server
2. Verify `OPENWEBUI_API_KEY` is correctly set
3. Ensure API key format matches server expectations
4. Check server logs for authentication details

### Functional Issues

#### No Search Results
**Problem**: Search returns empty results
**Solutions**:
1. Verify codebase is processed: `"get stats for codebase_name"`
2. Check codebase selection: `"select codebase correct_name"`
3. Try broader search terms
4. Remove language filters temporarily

#### Tool Commands Not Working
**Problem**: Tool doesn't respond to commands
**Solutions**:
1. Check if tool is enabled for the current model
2. Verify model has tool calling capabilities
3. Try explicit tool invocation: Click the tool button in chat
4. Check OpenWebUI logs for tool execution errors

#### Slow Performance
**Problem**: Tool responses are very slow
**Solutions**:
1. Increase `REQUEST_TIMEOUT` in tool configuration
2. Reduce `DEFAULT_MAX_RESULTS` for faster searches
3. Check server performance and resource usage
4. Use more specific search terms

### Debug Mode

Enable debug mode for detailed troubleshooting:

```python
ENABLE_DEBUG_MODE = True
```

Debug mode provides:
- Detailed request/response information
- Timing metrics for each operation
- Server communication details
- Error stack traces

### Log Analysis

Check OpenWebUI logs for tool-related issues:

```bash
# Docker deployment
docker logs openwebui

# Check for tool-specific errors
docker logs openwebui | grep "code_analyzer"
```

## 🔄 Updates and Maintenance

### Updating the Tool

1. Download the latest version of `open_webui_code_analyzer_tool.py`
2. In OpenWebUI Tools section, find the existing tool
3. Click **"Edit"** or **"Update"**
4. Replace the code with the new version
5. Save and test functionality

### Version Compatibility

| Tool Version | OpenWebUI Version | Server Version |
|--------------|-------------------|----------------|
| 3.0.0+       | 0.4.0+           | 3.0.0+         |
| 2.x.x        | 0.3.x            | 2.x.x          |
| 1.x.x        | 0.2.x            | 1.x.x          |

### Backup and Migration

Before major updates:

1. Export tool configuration from OpenWebUI
2. Document custom valve settings
3. Test new version in development environment
4. Plan rollback procedure if needed

## 🎯 Best Practices

### Effective Usage
1. **Start with status check**: Always verify server connectivity first
2. **Process before search**: Ensure codebases are indexed before searching
3. **Use specific terms**: More specific searches yield better results
4. **Leverage language filters**: Use language-specific searches for precision

### Performance Optimization
1. **Limit result count**: Use appropriate number of results for your needs
2. **Cache frequent queries**: Server caches results for better performance
3. **Use batch operations**: Process multiple codebases together when possible
4. **Monitor resource usage**: Check server performance regularly

### Security Considerations
1. **Secure API keys**: Use environment variables for sensitive configuration
2. **Network security**: Ensure secure communication between components
3. **Access control**: Limit tool access to authorized users only
4. **Regular updates**: Keep tool and server versions current

This integration guide provides comprehensive coverage of OpenWebUI tool installation, configuration, and usage for effective code analysis through natural language interaction.
