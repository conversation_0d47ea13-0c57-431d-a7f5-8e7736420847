{"data_mtime": 1753286522, "dep_lines": [13, 14, 15, 16, 17, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["re", "typing", "dataclasses", "enum", "logging", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "a4618b137bc20dd8b9e76905ba179fbe66031550", "id": "semantic_chunking_enhancement", "ignore_all": true, "interface_hash": "d780bc02259205156b2c529eb0e0c3ec3d60d2fa", "mtime": 1753275838, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\semantic_chunking_enhancement.py", "plugin_data": null, "size": 36362, "suppressed": [], "version_id": "1.15.0"}