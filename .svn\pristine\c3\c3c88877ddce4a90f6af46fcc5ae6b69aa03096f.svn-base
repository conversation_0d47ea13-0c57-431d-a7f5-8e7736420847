#!/usr/bin/env python3
"""
Reindex all vector databases on home-ai-server with proper Ollama embeddings
This script should be run on home-ai-server in the code_analyzer_server directory
"""

import sys
import os
import asyncio
import shutil
import time
from pathlib import Path

# Set the correct environment variables for home-ai-server
os.environ['OLLAMA_HOST'] = 'http://home-ai-server.local:11434'
os.environ['USE_OLLAMA_EMBEDDINGS'] = 'true'
os.environ['EMBEDDING_MODEL'] = 'nomic-embed-text'

def clear_vector_databases():
    """Clear all existing vector databases"""
    print("🗑️ CLEARING ALL VECTOR DATABASES")
    print("=" * 60)
    
    chroma_db_path = "./chroma_db"
    
    if os.path.exists(chroma_db_path):
        try:
            shutil.rmtree(chroma_db_path)
            print(f"   ✅ Cleared vector database at {chroma_db_path}")
            return True
        except Exception as e:
            print(f"   ❌ Error clearing database: {e}")
            return False
    else:
        print(f"   ℹ️ No existing database found at {chroma_db_path}")
        return True

def discover_codebases():
    """Discover all available codebases in source_code directory"""
    print("\n🔍 DISCOVERING CODEBASES")
    print("=" * 60)
    
    source_code_path = "./source_code"
    if not os.path.exists(source_code_path):
        print(f"   ❌ Source code directory not found: {source_code_path}")
        return []
    
    codebases = []
    for item in os.listdir(source_code_path):
        item_path = os.path.join(source_code_path, item)
        if os.path.isdir(item_path):
            codebases.append(item)
            print(f"   📚 Found codebase: {item}")
    
    print(f"   📊 Total codebases found: {len(codebases)}")
    return codebases

async def reindex_codebase(codebase_name, service):
    """Reindex a single codebase"""
    print(f"\n🔄 REINDEXING CODEBASE: {codebase_name}")
    print("=" * 60)
    
    try:
        start_time = time.time()
        
        # Check if codebase directory exists
        codebase_path = f"./source_code/{codebase_name}"
        if not os.path.exists(codebase_path):
            print(f"   ❌ Codebase directory not found: {codebase_path}")
            return False
        
        print(f"   ✅ Codebase directory found: {codebase_path}")
        
        # Process the codebase
        print(f"   🏗️ Processing {codebase_name}...")
        
        result = await service.process_codebase(
            codebase_name=codebase_name,
            exclude_dirs=["build", "test", "__pycache__", ".git", ".svn", "node_modules", "target", "bin", "obj"]
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if result.get('success', False):
            chunk_count = result.get('chunk_count', 0)
            print(f"   ✅ Successfully processed {codebase_name}!")
            print(f"   📊 Created {chunk_count} chunks")
            print(f"   ⏱️ Processing time: {processing_time:.2f} seconds")
            return True
        else:
            error = result.get('error', 'Unknown error')
            print(f"   ❌ Processing failed for {codebase_name}: {error}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error processing {codebase_name}: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_codebase_search(codebase_name, service):
    """Test search functionality for a codebase"""
    print(f"\n🧪 TESTING SEARCH: {codebase_name}")
    print("=" * 60)
    
    try:
        # Define test queries based on codebase type
        test_queries = {
            'utils': ['tmwmem_lowFree', 'memory management', 'TMWMEM_POOL_STRUCT'],
            'modbus': ['modbus', 'register', 'function code'],
            'z80emu': ['z80', 'emulator', 'instruction'],
            'networking_project': ['network', 'socket', 'connection'],
            'test_project': ['test', 'function', 'main']
        }
        
        # Use generic queries if codebase not recognized
        queries = test_queries.get(codebase_name, ['function', 'main', 'class'])
        
        success_count = 0
        for query in queries:
            print(f"   📝 Testing query: '{query}'")
            try:
                chunks = service.search(query, codebase_name, n_results=3)
                if chunks:
                    print(f"      ✅ Found {len(chunks)} results")
                    success_count += 1
                else:
                    print(f"      ⚠️ No results found")
            except Exception as e:
                print(f"      ❌ Query failed: {e}")
        
        success_rate = success_count / len(queries) * 100
        print(f"   📊 Search success rate: {success_count}/{len(queries)} ({success_rate:.1f}%)")
        return success_rate >= 50  # At least 50% success rate
        
    except Exception as e:
        print(f"   ❌ Error testing search for {codebase_name}: {e}")
        return False

async def main():
    """Main function to reindex all databases"""
    print("🚀 REINDEXING ALL VECTOR DATABASES ON HOME-AI-SERVER")
    print("=" * 80)
    print(f"🏠 Server: home-ai-server.local")
    print(f"🔗 Ollama Host: {os.environ['OLLAMA_HOST']}")
    print(f"🤖 Embedding Model: {os.environ['EMBEDDING_MODEL']}")
    print("=" * 80)
    
    # Step 1: Clear existing databases
    clear_success = clear_vector_databases()
    if not clear_success:
        print("\n❌ FAILED: Could not clear existing databases")
        return
    
    # Step 2: Discover codebases
    codebases = discover_codebases()
    if not codebases:
        print("\n❌ FAILED: No codebases found to index")
        return
    
    # Step 3: Initialize the service
    print(f"\n🔧 INITIALIZING CODE ANALYZER SERVICE")
    print("=" * 60)
    
    try:
        # Import after setting environment variables
        sys.path.append('.')
        from main import code_analyzer_service
        print("   ✅ Service initialized successfully")
    except Exception as e:
        print(f"   ❌ Error initializing service: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 4: Reindex all codebases
    print(f"\n🔄 REINDEXING {len(codebases)} CODEBASES")
    print("=" * 80)
    
    successful_codebases = []
    failed_codebases = []
    total_chunks = 0
    total_time = 0
    
    for i, codebase in enumerate(codebases, 1):
        print(f"\n[{i}/{len(codebases)}] Processing: {codebase}")
        
        start_time = time.time()
        success = await reindex_codebase(codebase, code_analyzer_service)
        end_time = time.time()
        
        processing_time = end_time - start_time
        total_time += processing_time
        
        if success:
            successful_codebases.append(codebase)
            # Test search functionality
            search_success = await test_codebase_search(codebase, code_analyzer_service)
            if search_success:
                print(f"   ✅ Search test passed for {codebase}")
            else:
                print(f"   ⚠️ Search test had issues for {codebase}")
        else:
            failed_codebases.append(codebase)
    
    # Step 5: Final summary
    print(f"\n" + "=" * 80)
    print("📊 REINDEXING SUMMARY")
    print("=" * 80)
    print(f"✅ Successfully indexed: {len(successful_codebases)} codebases")
    for codebase in successful_codebases:
        print(f"   - {codebase}")
    
    if failed_codebases:
        print(f"\n❌ Failed to index: {len(failed_codebases)} codebases")
        for codebase in failed_codebases:
            print(f"   - {codebase}")
    
    print(f"\n⏱️ Total processing time: {total_time:.2f} seconds")
    print(f"📈 Success rate: {len(successful_codebases)}/{len(codebases)} ({len(successful_codebases)/len(codebases)*100:.1f}%)")
    
    if len(successful_codebases) == len(codebases):
        print(f"\n🎉 SUCCESS: All vector databases reindexed successfully!")
        print("   🟢 System is ready for production use")
        print("   🟢 OpenWebUI integration should work")
        print("   🟢 All codebases are searchable")
    elif successful_codebases:
        print(f"\n⚠️ PARTIAL SUCCESS: {len(successful_codebases)} out of {len(codebases)} codebases indexed")
        print("   🟡 Some codebases may need manual attention")
    else:
        print(f"\n❌ FAILED: No codebases were successfully indexed")
        print("   🔴 System needs troubleshooting")

if __name__ == "__main__":
    asyncio.run(main())
