"""
Test intent detection functionality.
"""
import pytest
import httpx


class TestIntentDetection:
    """Test intent detection service endpoints."""

    @pytest.mark.asyncio
    async def test_detect_intent(self, http_client: httpx.AsyncClient, server_health_check):
        """Test the intent detection endpoint."""
        payload = {"query": "show me memory management functions"}
        response = await http_client.post("/tools/detect_intent", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "intent" in data
        assert "confidence" in data
        assert "matched_patterns" in data

    @pytest.mark.asyncio
    async def test_intent_config(self, http_client: httpx.AsyncClient):
        """Test getting intent detection configuration."""
        response = await http_client.get("/tools/intent_config")
        assert response.status_code == 200

        data = response.json()
        # The actual response contains config details, not a "config_summary" field
        assert isinstance(data, dict)
        assert "config_path" in data or "error" in data

    @pytest.mark.asyncio
    async def test_reload_intent_config(self, http_client: httpx.AsyncClient):
        """Test reloading intent detection configuration."""
        response = await http_client.post("/tools/intent_config/reload")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data or "error" in data

    @pytest.mark.asyncio
    async def test_intent_detection_with_various_queries(self, http_client: httpx.AsyncClient):
        """Test intent detection with various query types."""
        test_queries = [
            "help me understand this code",
            "search for memory functions",
            "what are the main classes?",
            "show me the API endpoints",
            "list all available codebases"
        ]
        
        for query in test_queries:
            payload = {"query": query}
            response = await http_client.post("/tools/detect_intent", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "intent" in data
            assert isinstance(data["confidence"], (int, float))

    @pytest.mark.asyncio
    async def test_intent_detection_empty_query(self, http_client: httpx.AsyncClient):
        """Test intent detection with empty query."""
        payload = {"query": ""}
        response = await http_client.post("/tools/detect_intent", json=payload)
        # Empty query might return 400 Bad Request, which is acceptable
        assert response.status_code in [200, 400]

        if response.status_code == 200:
            data = response.json()
            assert "intent" in data
        else:
            # 400 is acceptable for empty query
            pass
