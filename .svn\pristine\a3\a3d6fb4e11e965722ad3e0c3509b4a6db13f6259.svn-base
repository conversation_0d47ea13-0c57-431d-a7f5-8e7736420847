"""
Basic connectivity test to verify the server is accessible.
This is a simple test that can be run first to ensure the server is up.
"""
import pytest
import httpx
import asyncio


class TestBasicConnectivity:
    """Basic connectivity tests."""

    @pytest.mark.asyncio
    async def test_server_is_running(self):
        """Test that the server is running and accessible."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get("http://home-ai-server.local:5002/")
                assert response.status_code == 200
                print(f"✅ Server is running and responded with status {response.status_code}")
        except httpx.ConnectError:
            pytest.fail("❌ Server is not running on home-ai-server.local:5002. Please start the server first.")
        except Exception as e:
            pytest.fail(f"❌ Unexpected error connecting to server: {e}")

    @pytest.mark.asyncio
    async def test_health_endpoint_basic(self):
        """Test basic health endpoint accessibility."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get("http://home-ai-server.local:5002/health")
                assert response.status_code == 200
                data = response.json()
                # The health endpoint returns various health metrics
                assert isinstance(data, dict)
                assert len(data) > 0  # Should have some health data
                print(f"✅ Health endpoint accessible, found {len(data)} health metrics")
        except Exception as e:
            pytest.fail(f"❌ Health endpoint not accessible: {e}")

    @pytest.mark.asyncio
    async def test_list_codebases_basic(self):
        """Test basic codebase listing functionality."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post("http://home-ai-server.local:5002/tools/list_codebases")
                assert response.status_code == 200
                data = response.json()
                # The endpoint returns either "codebases" or "result" with formatted text
                assert "codebases" in data or "result" in data
                if "result" in data:
                    # Count codebases mentioned in the result text
                    codebase_count = data["result"].count("🚀")
                    print(f"✅ Codebases endpoint accessible, found {codebase_count} codebases in formatted result")
                else:
                    print(f"✅ Codebases endpoint accessible, found {len(data.get('codebases', []))} codebases")
        except Exception as e:
            pytest.fail(f"❌ Codebases endpoint not accessible: {e}")


if __name__ == "__main__":
    # Allow running this test file directly for quick checks
    asyncio.run(TestBasicConnectivity().test_server_is_running())
