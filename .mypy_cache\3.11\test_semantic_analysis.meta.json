{"data_mtime": 1753187349, "dep_lines": [7, 8, 9, 10, 11, 12, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "os", "json", "time", "typing", "pathlib", "semantic_chunking_enhancement", "tree_sitter_chunker", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.encoder", "types", "typing_extensions"], "hash": "ea8caae5646550f15a9884f1760aa78dd051bfe7", "id": "test_semantic_analysis", "ignore_all": false, "interface_hash": "f29dc3d902b1b75a665858b0992d8bbe77ed4e3e", "mtime": 1753187348, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_semantic_analysis.py", "plugin_data": null, "size": 14158, "suppressed": [], "version_id": "1.15.0"}