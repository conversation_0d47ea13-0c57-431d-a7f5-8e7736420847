"""
Generic Search Ranking System
Language-agnostic search ranking that works across all codebases and programming languages
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ContentType(Enum):
    """Generic content types detected across all languages"""
    IMPLEMENTATION = "implementation"      # Actual code implementation
    DECLARATION = "declaration"           # Function/class declarations
    DOCUMENTATION = "documentation"       # Comments, docstrings
    CONFIGURATION = "configuration"       # Config files, settings
    METADATA = "metadata"                # System metadata, overviews
    INTERFACE = "interface"              # API definitions, interfaces

class QueryIntent(Enum):
    """User intent behind the query - universal across all languages"""
    FIND_IMPLEMENTATION = "find_implementation"  # Looking for actual code
    UNDERSTAND_API = "understand_api"           # Looking for usage/docs
    EXPLORE_ARCHITECTURE = "explore_architecture" # Looking for design/structure
    DEBUG_ISSUE = "debug_issue"                 # Looking for problem solving
    LEARN_CONCEPT = "learn_concept"             # Looking for explanations

@dataclass
class GenericSearchContext:
    """Generic search context that works across all languages"""
    query: str
    intent: QueryIntent
    target_identifiers: List[str]  # Functions, classes, variables, etc.
    query_terms: List[str]         # Meaningful words from query
    language_hints: List[str]      # Programming language hints
    complexity_preference: str     # 'simple', 'detailed', 'comprehensive'

class GenericSearchRanker:
    """Universal search ranking system that works across all programming languages and codebases"""
    
    def __init__(self):
        # Universal patterns that work across ALL programming languages
        # These patterns are designed to be as generic as possible

        # Implementation indicators - signs of actual executable/synthesizable code
        self.implementation_indicators = [
            # Control flow (universal concepts)
            r'\breturn\b',                   # Return statements (most languages)
            r'\bif\b.*?[:\{]',              # Conditional statements
            r'\bfor\b.*?[:\{]',             # For loops
            r'\bwhile\b.*?[:\{]',           # While loops
            r'\belse\b.*?[:\{]',            # Else statements
            r'\btry\b.*?[:\{]',             # Exception handling
            r'\bcatch\b.*?[:\{]',           # Exception catching

            # Assignment and operations (universal)
            r'[a-zA-Z_]\w*\s*[=]\s*[^=]',   # Variable assignments
            r'[a-zA-Z_]\w*\s*[+\-*/]\s*=',  # Compound assignments
            r'[a-zA-Z_]\w*\s*\+\+',         # Increment operations
            r'[a-zA-Z_]\w*\s*--',           # Decrement operations

            # Code blocks (various styles)
            r'\{[^}]*\}',                   # Brace-delimited blocks
            r':\s*$.*?^(?=\S)',             # Indented blocks (Python-style)
            r'\bbegin\b.*?\bend\b',         # Begin-end blocks (Pascal, Ruby, VHDL)

            # Function calls and method invocations
            r'[a-zA-Z_]\w*\s*\([^)]*\)',    # Function calls
            r'[a-zA-Z_]\w*\.[a-zA-Z_]\w*\s*\([^)]*\)', # Method calls

            # Memory and resource operations (where applicable)
            r'\bnew\b\s+\w+',               # Object instantiation
            r'\bdelete\b\s+\w+',            # Memory deallocation
            r'\balloc\b',                   # Memory allocation
            r'\bfree\b\s*\(',               # Memory freeing

            # Hardware Description Language (HDL) specific patterns
            # VHDL patterns
            r'\bprocess\b.*?\bbegin\b',      # VHDL process blocks
            r'\barchitecture\b.*?\bof\b',    # VHDL architecture implementations
            r'\bwhen\b.*?\belse\b',          # VHDL conditional assignments
            r'\bcase\b.*?\bis\b',            # VHDL case statements
            r'\bgenerate\b.*?\bend\s+generate\b', # VHDL generate blocks
            r'\bcomponent\b.*?\bend\s+component\b', # VHDL component declarations
            r'\bsignal\b.*?:\s*\w+',         # VHDL signal declarations
            r'\bvariable\b.*?:\s*\w+',       # VHDL variable declarations
            r'\bport\s+map\b',               # VHDL port mappings
            r'\bgeneric\s+map\b',            # VHDL generic mappings

            # Verilog/SystemVerilog patterns
            r'\balways\b.*?@',               # Verilog always blocks
            r'\balways_ff\b.*?@',            # SystemVerilog always_ff blocks
            r'\balways_comb\b',              # SystemVerilog always_comb blocks
            r'\binitial\b.*?\bbegin\b',      # Verilog initial blocks
            r'\bmodule\b.*?\bendmodule\b',   # Verilog module implementations
            r'\bassign\b.*?=',               # Verilog continuous assignments
            r'\breg\b.*?;',                  # Verilog register declarations
            r'\bwire\b.*?;',                 # Verilog wire declarations
            r'\binput\b.*?;',                # Verilog input declarations
            r'\boutput\b.*?;',               # Verilog output declarations
            r'\binout\b.*?;',                # Verilog inout declarations
            r'\bposedge\b|\bnegdge\b',       # Verilog clock edges
            r'\bcase\b.*?\bendcase\b',       # Verilog case statements
            r'\bgenerate\b.*?\bendgenerate\b', # Verilog generate blocks

            # SystemC patterns
            r'\bSC_MODULE\b',                # SystemC module declarations
            r'\bSC_METHOD\b|\bSC_THREAD\b',  # SystemC processes
            r'\bsc_signal\b',                # SystemC signals
            r'\bsc_clock\b',                 # SystemC clocks
            r'\bwait\b\s*\(',                # SystemC wait statements

            # Chisel (Scala-based HDL) patterns
            r'\bclass\b.*?\bextends\b.*?\bModule\b', # Chisel modules
            r'\bval\b.*?=\s*Wire\b',         # Chisel wire declarations
            r'\bval\b.*?=\s*Reg\b',          # Chisel register declarations
            r'\bwhen\b.*?\b\.otherwise\b',   # Chisel conditional statements
            r'\bswitch\b.*?\bis\b',          # Chisel switch statements
        ]

        # Declaration indicators - function/class/type/module definitions
        self.declaration_indicators = [
            # Function definitions (various syntaxes)
            r'\b(?:def|function|fn|func|procedure|sub|method)\s+[a-zA-Z_]\w*\s*\(',
            r'\b[a-zA-Z_]\w*\s*\([^)]*\)\s*[:\{]',  # Generic function signature
            r'\bpublic\s+(?:static\s+)?[a-zA-Z_]\w*\s+[a-zA-Z_]\w*\s*\(',  # Java/C# methods
            r'\b[a-zA-Z_]\w*\s+[a-zA-Z_]\w*\s*\([^)]*\)\s*[;\{]',  # C/C++ functions

            # Type definitions (software languages)
            r'\b(?:class|struct|interface|enum|type|record)\s+[a-zA-Z_]\w*',
            r'\btypedef\s+.*?[a-zA-Z_]\w*',  # C typedefs
            r'\bdata\s+[a-zA-Z_]\w*',        # Haskell data types

            # Variable declarations (software languages)
            r'\b(?:var|let|const|dim|declare)\s+[a-zA-Z_]\w*',
            r'\b[a-zA-Z_]\w*\s+[a-zA-Z_]\w*\s*[=;]',  # Typed variable declarations

            # Hardware Description Language (HDL) declarations
            # VHDL declarations
            r'\bentity\s+[a-zA-Z_]\w*\s+is\b',       # VHDL entity declarations
            r'\barchitecture\s+[a-zA-Z_]\w*\s+of\s+[a-zA-Z_]\w*\s+is\b', # VHDL architecture declarations
            r'\bpackage\s+[a-zA-Z_]\w*\s+is\b',      # VHDL package declarations
            r'\bfunction\s+[a-zA-Z_]\w*\s*\([^)]*\)\s+return\b', # VHDL function declarations
            r'\bprocedure\s+[a-zA-Z_]\w*\s*\([^)]*\)\s+is\b', # VHDL procedure declarations
            r'\bcomponent\s+[a-zA-Z_]\w*\s+is\b',    # VHDL component declarations
            r'\btype\s+[a-zA-Z_]\w*\s+is\b',         # VHDL type declarations
            r'\bsubtype\s+[a-zA-Z_]\w*\s+is\b',      # VHDL subtype declarations
            r'\bconstant\s+[a-zA-Z_]\w*\s*:\s*\w+',  # VHDL constant declarations
            r'\bsignal\s+[a-zA-Z_]\w*\s*:\s*\w+',    # VHDL signal declarations
            r'\bvariable\s+[a-zA-Z_]\w*\s*:\s*\w+',  # VHDL variable declarations
            r'\bport\s*\([^)]*\)',                   # VHDL port declarations
            r'\bgeneric\s*\([^)]*\)',                # VHDL generic declarations

            # Verilog/SystemVerilog declarations
            r'\bmodule\s+[a-zA-Z_]\w*\s*[\(#]',      # Verilog module declarations
            r'\binterface\s+[a-zA-Z_]\w*\s*[\(;]',   # SystemVerilog interface declarations
            r'\bpackage\s+[a-zA-Z_]\w*\s*;',         # SystemVerilog package declarations
            r'\bclass\s+[a-zA-Z_]\w*\s*[;#]',        # SystemVerilog class declarations
            r'\bfunction\s+(?:\w+\s+)?[a-zA-Z_]\w*\s*\(', # Verilog/SystemVerilog function declarations
            r'\btask\s+[a-zA-Z_]\w*\s*[\(;]',        # Verilog/SystemVerilog task declarations
            r'\btypedef\s+(?:enum|struct|union)\s+[a-zA-Z_]\w*', # SystemVerilog typedef declarations
            r'\bparameter\s+[a-zA-Z_]\w*\s*=',       # Verilog parameter declarations
            r'\blocalparam\s+[a-zA-Z_]\w*\s*=',      # Verilog localparam declarations
            r'\binput\s+(?:\w+\s+)?[a-zA-Z_]\w*',    # Verilog input declarations
            r'\boutput\s+(?:\w+\s+)?[a-zA-Z_]\w*',   # Verilog output declarations
            r'\binout\s+(?:\w+\s+)?[a-zA-Z_]\w*',    # Verilog inout declarations
            r'\breg\s+(?:\[\d+:\d+\]\s+)?[a-zA-Z_]\w*', # Verilog register declarations
            r'\bwire\s+(?:\[\d+:\d+\]\s+)?[a-zA-Z_]\w*', # Verilog wire declarations
            r'\blogic\s+(?:\[\d+:\d+\]\s+)?[a-zA-Z_]\w*', # SystemVerilog logic declarations

            # SystemC declarations
            r'\bSC_MODULE\s*\(\s*[a-zA-Z_]\w*\s*\)', # SystemC module declarations
            r'\bsc_signal\s*<[^>]*>\s+[a-zA-Z_]\w*', # SystemC signal declarations
            r'\bsc_in\s*<[^>]*>\s+[a-zA-Z_]\w*',     # SystemC input port declarations
            r'\bsc_out\s*<[^>]*>\s+[a-zA-Z_]\w*',    # SystemC output port declarations
            r'\bsc_inout\s*<[^>]*>\s+[a-zA-Z_]\w*',  # SystemC inout port declarations
            r'\bsc_clock\s+[a-zA-Z_]\w*',            # SystemC clock declarations

            # Chisel (Scala-based HDL) declarations
            r'\bclass\s+[a-zA-Z_]\w*\s+extends\s+Module\b', # Chisel module declarations
            r'\bclass\s+[a-zA-Z_]\w*\s+extends\s+Bundle\b', # Chisel bundle declarations
            r'\bobject\s+[a-zA-Z_]\w*\s+extends\s+App\b',   # Chisel app objects
            r'\bval\s+[a-zA-Z_]\w*\s*=\s*(?:Wire|Reg|Input|Output)\b', # Chisel hardware declarations
            r'\bval\s+io\s*=\s*IO\s*\(',             # Chisel IO declarations

            # SpinalHDL (Scala-based) declarations
            r'\bclass\s+[a-zA-Z_]\w*\s+extends\s+Component\b', # SpinalHDL component declarations
            r'\bval\s+[a-zA-Z_]\w*\s*=\s*(?:Bool|UInt|SInt|Bits)\b', # SpinalHDL signal declarations
        ]

        # Documentation indicators - comments and documentation
        self.documentation_indicators = [
            # Multi-line comments (various styles)
            r'/\*.*?\*/',                   # C-style block comments
            r'\(\*.*?\*\)',                 # Pascal/ML comments
            r'\{-.*?-\}',                   # Haskell comments

            # Documentation comments
            r'/\*\*.*?\*/',                 # JavaDoc
            r'///.*?$',                     # C# XML documentation
            r'""".*?"""',                   # Python docstrings
            r"'''.*?'''",                   # Python docstrings

            # Single-line comments (software languages)
            r'//.*$',                       # C++ style
            r'#.*$',                        # Python/shell style
            r';.*$',                        # Lisp/assembly style
            r'%.*$',                        # Prolog/LaTeX style

            # Hardware Description Language (HDL) comments
            # VHDL comments
            r'--.*$',                       # VHDL single-line comments
            r'--\s*@.*$',                   # VHDL synthesis directives
            r'--\s*pragma.*$',              # VHDL pragma comments
            r'--\s*synthesis.*$',           # VHDL synthesis comments

            # Verilog/SystemVerilog comments
            r'//.*$',                       # Verilog single-line comments (same as C++)
            r'/\*.*?\*/',                   # Verilog multi-line comments (same as C)
            r'//\s*synthesis.*$',           # Verilog synthesis comments
            r'//\s*synopsys.*$',            # Synopsys tool comments
            r'//\s*cadence.*$',             # Cadence tool comments
            r'`ifdef.*?`endif',             # Verilog conditional compilation (documentation-like)
            r'`ifndef.*?`endif',            # Verilog conditional compilation

            # SystemC comments (C++ style)
            r'//.*$',                       # SystemC single-line comments
            r'/\*.*?\*/',                   # SystemC multi-line comments

            # Chisel/Scala comments
            r'//.*$',                       # Scala single-line comments
            r'/\*.*?\*/',                   # Scala multi-line comments
            r'/\*\*.*?\*/',                 # Scala ScalaDoc comments

            # Assembly language comments (various styles)
            r';.*$',                        # x86/ARM assembly comments
            r'#.*$',                        # Some assembly dialects
            r'@.*$',                        # ARM assembly comments
            r'\*.*$',                       # Some assembly dialects

            # Configuration and markup documentation
            r'<!--.*?-->',                  # HTML/XML comments
            r'\*.*?\*',                     # Markdown emphasis
            r'^\s*\*.*$',                   # Documentation block lines

            # HDL-specific documentation patterns
            r'--\s*Entity:\s*.*$',          # VHDL entity documentation
            r'--\s*Architecture:\s*.*$',    # VHDL architecture documentation
            r'--\s*Purpose:\s*.*$',         # HDL purpose documentation
            r'--\s*Author:\s*.*$',          # HDL author documentation
            r'--\s*Date:\s*.*$',            # HDL date documentation
            r'//\s*Module:\s*.*$',          # Verilog module documentation
            r'//\s*Description:\s*.*$',     # Verilog description documentation
        ]
        
        # Base ranking weights (can be adjusted per query)
        self.base_weights = {
            'exact_identifier_match': 10.0,
            'partial_identifier_match': 6.0,
            'implementation_content': 4.0,
            'declaration_content': 3.0,
            'documentation_content': 2.0,
            'query_term_density': 2.5,
            'language_match': 1.5,
            'content_quality': 1.2,
        }
        
        # Penalties for less relevant content
        self.penalties = {
            'metadata_only': -3.0,
            'architectural_overview': -2.0,
            'too_generic': -1.5,
            'wrong_language': -1.0,
        }

    def classify_query(self, query: str) -> GenericSearchContext:
        """Classify query intent and extract context - universal across all languages and domains"""
        query_lower = query.lower()

        # Determine user intent using comprehensive keyword analysis
        intent = QueryIntent.FIND_IMPLEMENTATION  # Default - most common use case

        # API/Documentation intent - user wants to understand how to use something
        api_keywords = ['how', 'usage', 'example', 'api', 'documentation', 'docs', 'tutorial',
                       'guide', 'reference', 'manual', 'help', 'syntax', 'parameters']
        if any(word in query_lower for word in api_keywords):
            intent = QueryIntent.UNDERSTAND_API

        # Architecture/Design intent - user wants high-level understanding
        arch_keywords = ['architecture', 'design', 'structure', 'overview', 'organization',
                        'layout', 'pattern', 'framework', 'system', 'overall', 'hierarchy']
        if any(word in query_lower for word in arch_keywords):
            intent = QueryIntent.EXPLORE_ARCHITECTURE

        # Debug/Problem-solving intent - user has an issue to resolve
        debug_keywords = ['error', 'bug', 'debug', 'fix', 'problem', 'issue', 'crash', 'fail',
                         'exception', 'troubleshoot', 'broken', 'not working', 'wrong']
        if any(word in query_lower for word in debug_keywords):
            intent = QueryIntent.DEBUG_ISSUE

        # Learning/Conceptual intent - user wants to understand concepts
        learn_keywords = ['what', 'explain', 'understand', 'concept', 'theory', 'principle',
                         'meaning', 'definition', 'purpose', 'why', 'difference', 'compare']
        if any(word in query_lower for word in learn_keywords):
            intent = QueryIntent.LEARN_CONCEPT
        
        # Extract identifiers (generic approach)
        identifiers = self._extract_identifiers(query)
        
        # Extract meaningful query terms
        query_terms = self._extract_meaningful_terms(query)
        
        # Detect language hints
        language_hints = self._detect_language_hints(query)
        
        # Determine complexity preference
        complexity = 'detailed'  # Default
        if any(word in query_lower for word in ['simple', 'basic', 'quick']):
            complexity = 'simple'
        elif any(word in query_lower for word in ['comprehensive', 'complete', 'full']):
            complexity = 'comprehensive'
        
        return GenericSearchContext(
            query=query,
            intent=intent,
            target_identifiers=identifiers,
            query_terms=query_terms,
            language_hints=language_hints,
            complexity_preference=complexity
        )

    def rank_results(self, results: List[Dict[str, Any]], context: GenericSearchContext) -> List[Dict[str, Any]]:
        """Rank search results based on generic relevance scoring"""
        if not results:
            return results
        
        scored_results = []
        
        for result in results:
            score = self._calculate_generic_score(result, context)
            content_type = self._classify_content_type(result)
            
            scored_results.append({
                'result': result,
                'relevance_score': score,
                'content_type': content_type,
                'ranking_explanation': self._explain_score(result, context, score)
            })
        
        # Sort by relevance score
        scored_results.sort(key=lambda x: float(x['relevance_score']) if isinstance(x['relevance_score'], (int, float)) else 0.0, reverse=True)

        # Log top results for debugging
        self._log_ranking_decision(scored_results[:3], context)

        return [item['result'] for item in scored_results if isinstance(item, dict) and 'result' in item and isinstance(item['result'], dict)]

    def _calculate_generic_score(self, result: Dict[str, Any], context: GenericSearchContext) -> float:
        """Calculate relevance score using generic, language-agnostic approach"""
        content = result.get('content', '').lower()
        metadata = result.get('metadata', {})
        
        base_score = 1.0
        
        # 1. Exact identifier matches (highest priority)
        for identifier in context.target_identifiers:
            if identifier.lower() in content:
                # Check if it's a definition/declaration (higher score)
                if re.search(rf'\b{re.escape(identifier)}\s*[\(\{{:]', content, re.IGNORECASE):
                    base_score *= self.base_weights['exact_identifier_match']
                else:
                    base_score *= self.base_weights['partial_identifier_match']
        
        # 2. Query term density
        term_matches = sum(1 for term in context.query_terms if term in content)
        if term_matches > 0:
            density = term_matches / len(context.query_terms) if context.query_terms else 0
            base_score *= (1.0 + density * self.base_weights['query_term_density'])
        
        # 3. Content type scoring based on intent
        content_type = self._classify_content_type(result)
        base_score *= self._get_intent_weight(content_type, context.intent)
        
        # 4. Language preference
        result_language = str(metadata.get('language', '')).lower()
        if context.language_hints and result_language in context.language_hints:
            base_score *= self.base_weights['language_match']
        
        # 5. Apply penalties
        base_score += self._calculate_generic_penalties(result, context)
        
        return max(base_score, 0.1)  # Minimum score

    def _classify_content_type(self, result: Dict[str, Any]) -> ContentType:
        """Classify content type using generic patterns"""
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        # Check for implementation code
        if any(re.search(pattern, content, re.MULTILINE | re.DOTALL) for pattern in self.implementation_indicators):
            return ContentType.IMPLEMENTATION
        
        # Check for declarations
        if any(re.search(pattern, content, re.IGNORECASE) for pattern in self.declaration_indicators):
            return ContentType.DECLARATION
        
        # Check for documentation
        if any(re.search(pattern, content, re.MULTILINE | re.DOTALL) for pattern in self.documentation_indicators):
            return ContentType.DOCUMENTATION
        
        # Check metadata indicators
        if str(metadata.get('language', '')).lower() == 'multi' or 'architectural' in str(metadata.get('chunk_type', '')):
            return ContentType.METADATA
        
        return ContentType.INTERFACE  # Default

    def _get_intent_weight(self, content_type: ContentType, intent: QueryIntent) -> float:
        """Get weight multiplier based on content type and user intent"""
        intent_weights = {
            QueryIntent.FIND_IMPLEMENTATION: {
                ContentType.IMPLEMENTATION: 3.0,
                ContentType.DECLARATION: 2.0,
                ContentType.DOCUMENTATION: 1.0,
                ContentType.METADATA: 0.3,
                ContentType.INTERFACE: 1.5,
            },
            QueryIntent.UNDERSTAND_API: {
                ContentType.DOCUMENTATION: 3.0,
                ContentType.DECLARATION: 2.5,
                ContentType.INTERFACE: 2.0,
                ContentType.IMPLEMENTATION: 1.5,
                ContentType.METADATA: 0.5,
            },
            QueryIntent.EXPLORE_ARCHITECTURE: {
                ContentType.METADATA: 3.0,
                ContentType.DOCUMENTATION: 2.0,
                ContentType.INTERFACE: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.IMPLEMENTATION: 1.0,
            },
            QueryIntent.DEBUG_ISSUE: {
                ContentType.IMPLEMENTATION: 3.0,
                ContentType.DOCUMENTATION: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.INTERFACE: 1.0,
                ContentType.METADATA: 0.5,
            },
            QueryIntent.LEARN_CONCEPT: {
                ContentType.DOCUMENTATION: 3.0,
                ContentType.IMPLEMENTATION: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.INTERFACE: 1.5,
                ContentType.METADATA: 1.0,
            }
        }
        
        return intent_weights.get(intent, {}).get(content_type, 1.0)

    def _extract_identifiers(self, query: str) -> List[str]:
        """Extract identifiers using generic patterns that work across all languages including HDLs"""
        patterns = [
            # Software language patterns
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',           # function_name(
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.[a-zA-Z_]',     # object.method
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)::[a-zA-Z_]',     # namespace::function
            r'(?:class|struct|interface|enum)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # type definitions
            r'(?:def|function|fn|func|method)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # function definitions

            # Hardware Description Language (HDL) patterns
            r'(?:entity|architecture|module|component)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # HDL design units
            r'(?:signal|wire|reg|logic|variable|constant)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # HDL objects
            r'(?:input|output|inout)\s+(?:\[[^\]]*\]\s+)?([a-zA-Z_][a-zA-Z0-9_]*)',  # HDL ports
            r'(?:parameter|localparam|generic)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # HDL parameters
            r'\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',  # Port connections
        ]

        identifiers = []
        for pattern in patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            identifiers.extend(matches)

        # Also extract standalone identifiers that look like code
        # Include HDL naming conventions (often use underscores and numbers)
        standalone = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]{2,})\b', query)

        # Filter out common words but keep technical terms
        common_words = {'function', 'method', 'class', 'the', 'and', 'for', 'with', 'from', 'that', 'this'}
        identifiers.extend([id for id in standalone if id.lower() not in common_words])

        return list(set(identifiers))

    def _extract_meaningful_terms(self, query: str) -> List[str]:
        """Extract meaningful terms from query, filtering out noise"""
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', query.lower())
        return [word for word in words if word not in stop_words and len(word) > 2]

    def _detect_language_hints(self, query: str) -> List[str]:
        """Detect programming language hints in query - comprehensive approach"""
        query_lower = query.lower()
        detected = []

        # Instead of hardcoding languages, look for common language patterns
        # This approach scales to any language without modification

        # Look for explicit language mentions
        language_patterns = [
            r'\b([a-zA-Z]+)\s+(?:language|lang)\b',     # "python language", "java lang"
            r'\bin\s+([a-zA-Z]+)\b',                    # "in python", "in java"
            r'\b([a-zA-Z]+)\s+(?:code|programming)\b',  # "python code", "java programming"
            r'\b([a-zA-Z]+)\s+(?:function|method|class)\b', # "python function", "java class"
        ]

        for pattern in language_patterns:
            matches = re.findall(pattern, query_lower)
            detected.extend(matches)

        # Look for file extensions
        extension_pattern = r'\.([a-zA-Z0-9]+)\b'
        extensions = re.findall(extension_pattern, query)
        detected.extend(extensions)

        # Look for language-specific syntax patterns (without hardcoding languages)
        syntax_patterns = [
            # Software language patterns
            r'\bdef\s+\w+',      # Python-like function definitions
            r'\bfn\s+\w+',       # Rust-like function definitions
            r'\bfunc\s+\w+',     # Go-like function definitions
            r'\bfunction\s+\w+', # JavaScript-like function definitions
            r'\bpublic\s+class', # Java/C#-like class definitions
            r'\bstd::\w+',       # C++ standard library
            r'\b#include\b',     # C/C++ includes
            r'\bimport\s+\w+',   # Python/Java imports

            # Hardware Description Language (HDL) patterns
            r'\bentity\s+\w+\s+is\b',        # VHDL entity
            r'\barchitecture\s+\w+\s+of\b',  # VHDL architecture
            r'\bmodule\s+\w+\s*[\(#]',       # Verilog module
            r'\balways\s*@',                 # Verilog always block
            r'\balways_ff\s*@',              # SystemVerilog always_ff
            r'\balways_comb\b',              # SystemVerilog always_comb
            r'\bprocess\s*\(',               # VHDL process
            r'\bsignal\s+\w+\s*:\s*\w+',     # VHDL signal
            r'\breg\s+\w+',                  # Verilog register
            r'\bwire\s+\w+',                 # Verilog wire
            r'\blogic\s+\w+',                # SystemVerilog logic
            r'\binput\s+\w+',                # Verilog input
            r'\boutput\s+\w+',               # Verilog output
            r'\bposedge\b|\bnegdge\b',       # Verilog clock edges
            r'\bSC_MODULE\b',                # SystemC module
            r'\bSC_METHOD\b|\bSC_THREAD\b',  # SystemC processes
            r'\bextends\s+Module\b',         # Chisel module
            r'\bextends\s+Component\b',      # SpinalHDL component
            r'\bgenerate\b.*?\bendgenerate\b', # Verilog generate
            r'\bcase\b.*?\bendcase\b',       # Verilog case
            r'\bwhen\b.*?\belse\b',          # VHDL/Chisel when-else
            r'\bassign\s+\w+\s*=',           # Verilog continuous assignment
            r'<=\s*\w+',                     # HDL non-blocking assignment
            r':=\s*\w+',                     # VHDL variable assignment
        ]

        # If we find syntax patterns, we know it's a programming query
        # but we don't need to identify the specific language
        has_code_syntax = any(re.search(pattern, query, re.IGNORECASE) for pattern in syntax_patterns)
        if has_code_syntax:
            detected.append('code')  # Generic indicator that this involves programming

        return list(set(detected))  # Remove duplicates

    def _calculate_generic_penalties(self, result: Dict[str, Any], context: GenericSearchContext) -> float:
        """Calculate penalties for less relevant content"""
        penalty = 0.0
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        # Penalty for metadata-only content when looking for implementation
        if context.intent == QueryIntent.FIND_IMPLEMENTATION:
            if str(metadata.get('language', '')).lower() == 'multi':
                penalty += self.penalties['metadata_only']
            
            # Penalty for architectural overviews
            if 'architectural' in str(metadata.get('chunk_type', '')).lower():
                penalty += self.penalties['architectural_overview']
        
        # Penalty for content that's too generic (many function names but no implementation)
        function_count = len(re.findall(r'\b\w+\s*\(', content))
        if function_count > 15 and not any(re.search(pattern, content) for pattern in self.implementation_indicators):
            penalty += self.penalties['too_generic']
        
        return penalty

    def _explain_score(self, result: Dict[str, Any], context: GenericSearchContext, score: float) -> Dict[str, Any]:
        """Provide explanation for scoring decision"""
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        return {
            'final_score': round(score, 2),
            'content_type': self._classify_content_type(result).value,
            'identifier_matches': len([id for id in context.target_identifiers if id.lower() in content.lower()]),
            'query_term_matches': len([term for term in context.query_terms if term in content.lower()]),
            'language': metadata.get('language', 'unknown'),
            'intent_alignment': context.intent.value
        }

    def _log_ranking_decision(self, top_results: List[Dict[str, Any]], context: GenericSearchContext):
        """Log ranking decisions for debugging"""
        logger.info(f"🎯 Generic ranking for query: '{context.query}' (intent: {context.intent.value})")
        
        for i, item in enumerate(top_results):
            explanation = item['ranking_explanation']
            logger.info(f"  #{i+1}: Score={explanation['final_score']}, "
                       f"Type={explanation['content_type']}, "
                       f"Matches={explanation['identifier_matches']}, "
                       f"Lang={explanation['language']}")

# Global instance
generic_ranker = GenericSearchRanker()
