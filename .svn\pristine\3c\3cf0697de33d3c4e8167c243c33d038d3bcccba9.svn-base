"""
Test search ranking functionality and enhancements.
Tests the generic search ranking system, query classification, and result scoring.
"""
import pytest
import httpx
from typing import Dict, List, Any


@pytest.mark.search
@pytest.mark.ranking
@pytest.mark.framework
class TestSearchRanking:
    """Test search ranking and enhancement functionality."""

    @pytest.mark.asyncio
    async def test_enhanced_search_endpoint(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that enhanced search endpoint is available and functional."""
        response = await http_client.post("/tools/enhanced_search", json={
            "query": "memory management functions",
            "codebase_name": "utils",
            "n_results": 5
        })
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure - enhanced search returns different format
        assert "result" in data  # Enhanced search returns "result" not "results"
        assert isinstance(data["result"], str)

        # Enhanced search should include formatted results with ranking information
        result_text = data["result"]
        assert "Enhanced Search" in result_text
        assert "Found" in result_text and "results" in result_text
        assert "Relevance:" in result_text  # Should show relevance scores

    @pytest.mark.asyncio
    async def test_search_ranking_configuration(self, http_client: httpx.AsyncClient, server_health_check):
        """Test search ranking configuration endpoint."""
        # Test getting current configuration
        response = await http_client.get("/api/search/ranking_status")
        
        if response.status_code == 200:
            data = response.json()
            assert "enabled" in data or "status" in data
        else:
            # Configuration endpoint might not be implemented yet
            pytest.skip("Search ranking configuration endpoint not available")

    @pytest.mark.asyncio
    async def test_query_intent_classification(self, http_client: httpx.AsyncClient, server_health_check):
        """Test different query types to verify intent-based ranking."""
        test_queries = [
            {
                "query": "memory allocation function",
                "intent": "find_implementation",
                "expected_content_type": "implementation"
            },
            {
                "query": "how to use malloc",
                "intent": "understand_api", 
                "expected_content_type": "usage"
            },
            {
                "query": "memory management architecture",
                "intent": "explore_architecture",
                "expected_content_type": "architecture"
            },
            {
                "query": "memory leak debug",
                "intent": "debug_issue",
                "expected_content_type": "debugging"
            }
        ]
        
        for test_case in test_queries:
            response = await http_client.post("/search", json={
                "query": test_case["query"],
                "codebase_name": "utils",
                "n_results": 3
            })

            assert response.status_code == 200
            data = response.json()

            # Verify search returns results
            if "results" in data and data["results"]:
                # Results should be ranked appropriately for the query intent
                assert len(data["results"]) > 0

                # Check if results contain relevant content for the intent
                first_result = data["results"][0]
                content = str(first_result.get("content", "") + first_result.get("text", "")).lower()

                # Verify content relevance based on query intent
                query_terms = test_case["query"].lower().split()
                # Check for either exact query terms or related programming terms
                programming_terms = ["function", "method", "class", "struct", "malloc", "free", "alloc", "memory", "debug", "error"]
                relevant_terms_found = (
                    any(term in content for term in query_terms) or
                    any(term in content for term in programming_terms)
                )
                assert relevant_terms_found, f"No relevant programming terms found for query: {test_case['query']}"

    @pytest.mark.asyncio
    async def test_search_result_scoring(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that search results are properly scored and ranked."""
        response = await http_client.post("/search", json={
            "query": "malloc free memory",
            "codebase_name": "utils",
            "n_results": 10
        })
        
        assert response.status_code == 200
        data = response.json()
        
        if "results" in data and len(data["results"]) > 1:
            results = data["results"]
            
            # Verify results are returned
            assert len(results) > 0
            
            # Check that results contain memory-related content
            memory_related_count = 0
            for result in results[:5]:  # Check first 5 results
                content = str(result.get("content", "") + result.get("text", "")).lower()
                if any(term in content for term in ["malloc", "free", "memory", "alloc"]):
                    memory_related_count += 1
            
            # At least some results should be memory-related for this query
            assert memory_related_count > 0, "No memory-related results found for memory query"

    @pytest.mark.asyncio
    async def test_language_specific_ranking(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that search ranking works across different programming languages."""
        language_queries = [
            {"query": "function definition", "language_hint": "c"},
            {"query": "class implementation", "language_hint": "python"},
            {"query": "method declaration", "language_hint": "java"},
            {"query": "struct definition", "language_hint": "c"}
        ]
        
        for test_case in language_queries:
            response = await http_client.post("/search", json={
                "query": test_case["query"],
                "codebase_name": "utils",
                "n_results": 5
            })
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify search works for language-specific queries
            if "results" in data and data["results"]:
                assert len(data["results"]) > 0
                
                # Results should contain relevant programming constructs
                first_result = data["results"][0]
                content = str(first_result.get("content", "") + first_result.get("text", "")).lower()
                
                # Check for programming language constructs
                programming_indicators = ["function", "class", "method", "struct", "def", "int", "void", "public", "private"]
                has_programming_content = any(indicator in content for indicator in programming_indicators)
                assert has_programming_content, f"No programming content found for query: {test_case['query']}"

    @pytest.mark.asyncio
    async def test_search_ranking_performance(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that search ranking doesn't significantly impact performance."""
        import time
        
        query = "memory management"
        start_time = time.time()
        
        response = await http_client.post("/search", json={
            "query": query,
            "codebase_name": "utils",
            "n_results": 10
        })
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        
        # Search with ranking should complete within reasonable time (5 seconds)
        assert response_time < 5.0, f"Search ranking took too long: {response_time:.2f} seconds"
        
        data = response.json()
        if "results" in data:
            assert isinstance(data["results"], list)

    @pytest.mark.asyncio
    async def test_fallback_behavior(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that search falls back gracefully if ranking fails."""
        # Test with potentially problematic query
        response = await http_client.post("/search", json={
            "query": "!@#$%^&*()_+ invalid query with special chars",
            "codebase_name": "utils",
            "n_results": 5
        })
        
        # Should still return a valid response even with problematic query
        assert response.status_code in [200, 400]  # Either success or valid error
        
        if response.status_code == 200:
            data = response.json()
            assert "results" in data
            assert isinstance(data["results"], list)

    @pytest.mark.asyncio
    async def test_empty_query_handling(self, http_client: httpx.AsyncClient, server_health_check):
        """Test search ranking with empty or minimal queries."""
        test_cases = [
            {"query": "", "should_succeed": False},
            {"query": " ", "should_succeed": False},
            {"query": "a", "should_succeed": True},
            {"query": "function", "should_succeed": True}
        ]
        
        for test_case in test_cases:
            response = await http_client.post("/search", json={
                "query": test_case["query"],
                "codebase_name": "utils",
                "n_results": 5
            })
            
            if test_case["should_succeed"]:
                assert response.status_code == 200
                data = response.json()
                assert "results" in data
            else:
                # Empty queries should either return empty results or error gracefully
                assert response.status_code in [200, 400]

    @pytest.mark.asyncio
    async def test_search_ranking_metadata(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that search results include ranking metadata when available."""
        response = await http_client.post("/search", json={
            "query": "memory allocation",
            "codebase_name": "utils",
            "n_results": 3
        })
        
        assert response.status_code == 200
        data = response.json()
        
        if "results" in data and data["results"]:
            result = data["results"][0]
            
            # Check for standard result structure
            assert "metadata" in result or "content" in result or "text" in result
            
            # If enhanced ranking is active, there might be additional metadata
            if "metadata" in result:
                metadata = result["metadata"]
                assert isinstance(metadata, dict)
