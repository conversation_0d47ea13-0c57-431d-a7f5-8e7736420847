{"data_mtime": 1753187993, "dep_lines": [7, 8, 9, 10, 15, 16, 165, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "os", "json", "typing", "semantic_chunking_enhancement", "tree_sitter_chunker", "pathlib", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "types", "typing_extensions"], "hash": "1fd35cda20fede5b9d99b1878a0de039c4d4ad54", "id": "verify_semantic_fix", "ignore_all": false, "interface_hash": "d621bf52712300979b0b49b325b7c75be2c22fd3", "mtime": 1753187993, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\verify_semantic_fix.py", "plugin_data": null, "size": 8485, "suppressed": [], "version_id": "1.15.0"}