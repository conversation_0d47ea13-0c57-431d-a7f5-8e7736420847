"""
Test deployment and enhancement features.
Tests codebase processing, enhancement capabilities, and deployment validation.
"""
import pytest
import httpx
import asyncio


class TestDeploymentFeatures:
    """Test deployment and enhancement capabilities."""

    @pytest.mark.asyncio
    async def test_codebase_processing_capability(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that the server can process codebases with enhanced features."""
        # Test processing endpoint availability
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have processing capabilities
        if "management_tools" in data:
            tools = data["management_tools"]
            assert "process_codebase" in tools, "process_codebase tool should be available"

    @pytest.mark.asyncio
    async def test_enhanced_features_availability(self, http_client: httpx.AsyncClient):
        """Test that enhanced features are available and working."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Check for enhanced features
        if "enhanced_features" in data:
            features = data["enhanced_features"]
            expected_features = [
                "semantic_tagging",
                "quality_analysis", 
                "complexity_metrics",
                "optimized_context_retrieval"
            ]
            
            for feature in expected_features:
                assert feature in features, f"Enhanced feature {feature} should be available"
                
            print(f"✅ Enhanced features available: {len(features)}")

    @pytest.mark.asyncio
    async def test_codebase_enhancement_status(self, http_client: httpx.AsyncClient):
        """Test that codebases show proper enhancement status."""
        response = await http_client.post("/tools/list_codebases")
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data
        
        result = data["result"]
        
        # Should show enhancement status for codebases
        enhancement_indicators = [
            "enhanced",
            "ready_enhanced", 
            "v3.2",
            "metadata_version",
            "enhanced_metadata"
        ]
        
        found_indicators = [ind for ind in enhancement_indicators if ind in result.lower()]
        
        # Should have some enhancement status indicators
        assert len(found_indicators) > 0, "No enhancement status indicators found"
        
        print(f"✅ Enhancement status indicators: {found_indicators}")

    @pytest.mark.asyncio
    async def test_metadata_version_validation(self, http_client: httpx.AsyncClient):
        """Test that codebases have proper metadata versions."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            # Should have metadata version information
            if "metadata_version" in codebase_info:
                metadata_version = codebase_info["metadata_version"]
                assert metadata_version is not None
                
                # Should be a recent version
                if isinstance(metadata_version, str):
                    assert "v3" in metadata_version or "enhanced" in metadata_version.lower()
                    
                print(f"✅ Metadata version: {metadata_version}")

    @pytest.mark.asyncio
    async def test_enhancement_recommendation_system(self, http_client: httpx.AsyncClient):
        """Test the enhancement recommendation system."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            # Should have enhancement recommendation field
            assert "enhancement_recommendation" in codebase_info
            
            # If enhanced, recommendation should be None
            if codebase_info.get("has_enhanced_metadata"):
                assert codebase_info["enhancement_recommendation"] is None, \
                    "Enhanced codebases should not have enhancement recommendations"
                    
            print(f"✅ Enhancement recommendation: {codebase_info.get('enhancement_recommendation')}")

    @pytest.mark.asyncio
    async def test_codebase_complexity_analysis(self, http_client: httpx.AsyncClient):
        """Test codebase complexity analysis features."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            # Should have complexity analysis
            if "complexity_hint" in codebase_info:
                complexity = codebase_info["complexity_hint"]
                assert complexity in ["small", "medium", "large", "very_large"]
                
                print(f"✅ Complexity analysis: {complexity}")

    @pytest.mark.asyncio
    async def test_multi_language_detection(self, http_client: httpx.AsyncClient):
        """Test multi-language detection in codebases."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            # Should detect multiple languages
            if "detected_languages" in codebase_info:
                languages = codebase_info["detected_languages"]
                assert isinstance(languages, list)
                assert len(languages) > 0
                
                print(f"✅ Detected languages: {languages}")
            
            # Should have file counts by extension
            if "file_counts" in codebase_info:
                file_counts = codebase_info["file_counts"]
                assert isinstance(file_counts, dict)
                assert len(file_counts) > 0
                
                print(f"✅ File extensions: {list(file_counts.keys())}")

    @pytest.mark.asyncio
    async def test_chunk_count_accuracy(self, http_client: httpx.AsyncClient):
        """Test that chunk counts are accurate and reasonable."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            if "chunk_count" in codebase_info:
                chunk_count = codebase_info["chunk_count"]
                assert isinstance(chunk_count, int)
                assert chunk_count > 0, "Chunk count should be positive"
                
                # For utils codebase, should have substantial chunks
                if codebase_info.get("name") == "utils":
                    assert chunk_count > 100, f"Utils should have >100 chunks, got {chunk_count}"
                    
                print(f"✅ Chunk count: {chunk_count}")

    @pytest.mark.asyncio
    async def test_source_path_validation(self, http_client: httpx.AsyncClient):
        """Test that source paths are properly configured."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            if "source_path" in codebase_info:
                source_path = codebase_info["source_path"]
                assert isinstance(source_path, str)
                assert "/app/source_code" in source_path
                
                print(f"✅ Source path: {source_path}")

    @pytest.mark.asyncio
    async def test_database_availability(self, http_client: httpx.AsyncClient):
        """Test that vector databases are available for codebases."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            # Should have database availability info
            if "has_database" in codebase_info:
                has_database = codebase_info["has_database"]
                assert isinstance(has_database, bool)
                assert has_database, "Current codebase should have database available"
                
            if "has_source" in codebase_info:
                has_source = codebase_info["has_source"]
                assert isinstance(has_source, bool)
                assert has_source, "Current codebase should have source available"

    @pytest.mark.asyncio
    async def test_last_updated_tracking(self, http_client: httpx.AsyncClient):
        """Test that last updated timestamps are tracked."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            
            if "last_updated" in codebase_info:
                last_updated = codebase_info["last_updated"]
                assert isinstance(last_updated, str)
                
                # Should be a valid ISO timestamp
                import datetime
                try:
                    datetime.datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                    print(f"✅ Last updated: {last_updated}")
                except ValueError:
                    pytest.fail(f"Invalid timestamp format: {last_updated}")

    @pytest.mark.asyncio
    async def test_deployment_health_indicators(self, http_client: httpx.AsyncClient):
        """Test deployment health indicators."""
        response = await http_client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have deployment-related health info
        health_indicators = [
            "ollama_status",
            "code_analyzer_service", 
            "framework_available",
            "gpu_infrastructure"
        ]
        
        found_indicators = []
        for indicator in health_indicators:
            if indicator in str(data).lower():
                found_indicators.append(indicator)
        
        assert len(found_indicators) > 0, "No deployment health indicators found"
        
        print(f"✅ Health indicators: {found_indicators}")

    @pytest.mark.asyncio
    async def test_optimization_features(self, http_client: httpx.AsyncClient):
        """Test optimization features are enabled."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have optimization information
        if "optimization" in data:
            optimization = data["optimization"]
            assert isinstance(optimization, str)
            
            # Should mention optimization features
            optimization_keywords = ["optimization", "single", "llm", "processing"]
            found_keywords = [kw for kw in optimization_keywords if kw.lower() in optimization.lower()]
            
            assert len(found_keywords) > 0, "No optimization keywords found"
            
            print(f"✅ Optimization: {optimization}")

    @pytest.mark.asyncio
    async def test_version_information(self, http_client: httpx.AsyncClient):
        """Test that version information is available."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        if "version" in data:
            version = data["version"]
            assert isinstance(version, str)
            assert len(version) > 0
            
            # Should be a reasonable version format
            assert "." in version or version.isdigit()
            
            print(f"✅ Version: {version}")

    @pytest.mark.asyncio
    async def test_feature_completeness(self, http_client: httpx.AsyncClient):
        """Test that all expected features are available."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have comprehensive feature sets
        expected_sections = [
            "enhanced_features",
            "optimized_tools", 
            "legacy_tools",
            "management_tools"
        ]
        
        available_sections = [section for section in expected_sections if section in data]
        
        # Should have most expected sections
        assert len(available_sections) >= 3, f"Missing feature sections: {set(expected_sections) - set(available_sections)}"
        
        print(f"✅ Available feature sections: {available_sections}")

    @pytest.mark.asyncio
    async def test_deployment_configuration(self, http_client: httpx.AsyncClient):
        """Test deployment configuration is correct."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        
        # Should have proper Ollama configuration
        if "ollama_host" in data:
            ollama_host = data["ollama_host"]
            assert isinstance(ollama_host, str)
            assert "11434" in ollama_host  # Ollama default port
            
            print(f"✅ Ollama host: {ollama_host}")
        
        if "ollama_status" in data:
            ollama_status = data["ollama_status"]
            assert ollama_status in ["connected", "available", "ready"]
            
            print(f"✅ Ollama status: {ollama_status}")
