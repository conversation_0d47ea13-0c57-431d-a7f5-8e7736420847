"""
Pytest configuration and fixtures for the code analysis server tests.
"""
import pytest
import pytest_asyncio
import httpx
import asyncio
from typing import AsyncGenerator


# Test configuration
TEST_SERVER_URL = "http://home-ai-server.local:5002"
TEST_TIMEOUT = 30.0


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def http_client() -> AsyncGenerator[httpx.AsyncClient, None]:
    """Create an HTTP client for testing the server."""
    async with httpx.AsyncClient(
        base_url=TEST_SERVER_URL,
        timeout=TEST_TIMEOUT
    ) as client:
        yield client


@pytest_asyncio.fixture(scope="function")
async def server_health_check(http_client: httpx.AsyncClient):
    """Verify the server is running and healthy before running tests."""
    try:
        response = await http_client.get("/health")
        assert response.status_code == 200
        health_data = response.json()
        # The health endpoint returns various metrics, not necessarily a "status" field
        assert isinstance(health_data, dict) and len(health_data) > 0
        return health_data
    except Exception as e:
        pytest.skip(f"Server not available at {TEST_SERVER_URL}: {e}")


@pytest.fixture
async def sample_codebase_name():
    """Return a sample codebase name for testing."""
    return "utils"  # Based on the memories, this is a known codebase


@pytest.fixture
async def sample_query():
    """Return a sample query for testing."""
    return "memory management functions"
