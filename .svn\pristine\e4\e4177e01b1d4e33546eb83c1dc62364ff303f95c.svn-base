#!/usr/bin/env python3
"""
Unit tests specifically for the new semantic chunking integration
Tests the integration of chunk_file_with_semantics into the main processing pipeline
"""

import pytest
import sys
import os
from typing import Dict, List, Any
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tree_sitter_chunker import TreeSitterChunker
from framework_integration import ChunkG<PERSON>ation<PERSON>tage, IntegratedCodeAnalysisSystem
from semantic_chunking_enhancement import SemanticChunker

class TestSemanticChunkingIntegration:
    """Test suite for semantic chunking integration into the main pipeline"""
    
    @pytest.fixture
    def sample_c_code_large(self):
        """Large C code sample that should trigger semantic enhancement"""
        return '''
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024
#define MAX_CONNECTIONS 100

// Connection management structure
typedef struct connection {
    int socket_fd;
    char* buffer;
    size_t buffer_size;
    int is_active;
} connection_t;

static connection_t connections[MAX_CONNECTIONS];
static int connection_count = 0;

/**
 * Initialize connection management system
 * @return 0 on success, -1 on failure
 */
int connection_init(void) {
    memset(connections, 0, sizeof(connections));
    connection_count = 0;
    
    for (int i = 0; i < MAX_CONNECTIONS; i++) {
        connections[i].buffer = malloc(BUFFER_SIZE);
        if (!connections[i].buffer) {
            // Cleanup on failure
            for (int j = 0; j < i; j++) {
                free(connections[j].buffer);
            }
            return -1;
        }
        connections[i].buffer_size = BUFFER_SIZE;
        connections[i].is_active = 0;
    }
    
    return 0;
}

/**
 * Create new connection
 * @param socket_fd Socket file descriptor
 * @return Connection index or -1 on failure
 */
int connection_create(int socket_fd) {
    if (connection_count >= MAX_CONNECTIONS) {
        return -1;
    }
    
    int index = -1;
    for (int i = 0; i < MAX_CONNECTIONS; i++) {
        if (!connections[i].is_active) {
            index = i;
            break;
        }
    }
    
    if (index == -1) {
        return -1;
    }
    
    connections[index].socket_fd = socket_fd;
    connections[index].is_active = 1;
    connection_count++;
    
    return index;
}

/**
 * Close connection
 * @param index Connection index
 */
void connection_close(int index) {
    if (index < 0 || index >= MAX_CONNECTIONS) {
        return;
    }
    
    if (connections[index].is_active) {
        close(connections[index].socket_fd);
        memset(connections[index].buffer, 0, connections[index].buffer_size);
        connections[index].is_active = 0;
        connection_count--;
    }
}

/**
 * Send data through connection
 * @param index Connection index
 * @param data Data to send
 * @param length Data length
 * @return Bytes sent or -1 on error
 */
int connection_send(int index, const char* data, size_t length) {
    if (index < 0 || index >= MAX_CONNECTIONS || !connections[index].is_active) {
        return -1;
    }
    
    return send(connections[index].socket_fd, data, length, 0);
}

/**
 * Receive data from connection
 * @param index Connection index
 * @param buffer Buffer to store data
 * @param max_length Maximum bytes to receive
 * @return Bytes received or -1 on error
 */
int connection_receive(int index, char* buffer, size_t max_length) {
    if (index < 0 || index >= MAX_CONNECTIONS || !connections[index].is_active) {
        return -1;
    }
    
    return recv(connections[index].socket_fd, buffer, max_length, 0);
}

/**
 * Get connection statistics
 * @return Number of active connections
 */
int connection_get_active_count(void) {
    return connection_count;
}

/**
 * Cleanup all connections
 */
void connection_cleanup(void) {
    for (int i = 0; i < MAX_CONNECTIONS; i++) {
        if (connections[i].is_active) {
            connection_close(i);
        }
        if (connections[i].buffer) {
            free(connections[i].buffer);
            connections[i].buffer = NULL;
        }
    }
    connection_count = 0;
}
'''

    @pytest.fixture
    def chunker(self):
        """Create TreeSitterChunker instance"""
        return TreeSitterChunker()

    def test_semantic_chunking_integration_basic(self, chunker, sample_c_code_large):
        """Test that semantic chunking is properly integrated"""
        # Test basic chunking
        basic_chunks = chunker.chunk_file("test_connection.c", sample_c_code_large, "c_cpp")
        
        # Test semantic chunking
        semantic_chunks = chunker.chunk_file_with_semantics("test_connection.c", sample_c_code_large, "c_cpp")
        
        assert len(basic_chunks) > 0, "Should generate basic chunks"
        assert len(semantic_chunks) > 0, "Should generate semantic chunks"
        
        # Count semantic enhancements
        semantic_enhanced_count = sum(1 for chunk in semantic_chunks 
                                    if chunk.get('semantic_enhanced', False))
        
        print(f"   Basic chunks: {len(basic_chunks)}")
        print(f"   Semantic chunks: {len(semantic_chunks)}")
        print(f"   Semantically enhanced: {semantic_enhanced_count}")
        
        # For large C files, should have semantic enhancement
        assert semantic_enhanced_count > 0, "Large C file should have semantic enhancement"
        
        # Verify semantic chunks have proper metadata
        for chunk in semantic_chunks:
            if chunk.get('semantic_enhanced', False):
                assert 'context_level' in chunk, "Semantic chunks should have context_level"
                # Semantic chunker generates different context levels based on chunk type
                valid_context_levels = ['multi_level', 'signature', 'enhanced', 'function_level', 'file_level']
                assert chunk['context_level'] in valid_context_levels, f"Invalid context_level: {chunk['context_level']}"

    def test_semantic_chunking_metadata_preservation(self, chunker, sample_c_code_large):
        """Test that semantic chunking metadata is properly preserved"""
        semantic_chunks = chunker.chunk_file_with_semantics("test_connection.c", sample_c_code_large, "c_cpp")
        
        enhanced_chunks = [chunk for chunk in semantic_chunks if chunk.get('semantic_enhanced', False)]
        assert len(enhanced_chunks) > 0, "Should have enhanced chunks"
        
        for chunk in enhanced_chunks:
            # Verify required fields
            required_fields = ['content', 'file_path', 'chunk_type', 'function_name', 'semantic_enhanced', 'context_level']
            for field in required_fields:
                assert field in chunk, f"Enhanced chunk should have {field}"
            
            # Verify semantic enhancement flag
            assert chunk['semantic_enhanced'] is True, "Enhanced chunks should have semantic_enhanced=True"
            
            # Verify context level (semantic chunker generates various context levels)
            valid_context_levels = ['multi_level', 'signature', 'enhanced', 'function_level', 'file_level']
            assert chunk['context_level'] in valid_context_levels, f"Invalid context_level: {chunk['context_level']}"

    def test_semantic_chunking_fallback_behavior(self, chunker):
        """Test fallback behavior when semantic chunking fails or is not applicable"""
        # Small C code that might not trigger semantic enhancement
        small_c_code = '''
#include <stdio.h>

int main() {
    printf("Hello, World!\\n");
    return 0;
}
'''
        
        chunks = chunker.chunk_file_with_semantics("small.c", small_c_code, "c_cpp")
        assert len(chunks) > 0, "Should generate chunks even for small files"
        
        # Check if any chunks are semantically enhanced
        enhanced_count = sum(1 for chunk in chunks if chunk.get('semantic_enhanced', False))
        print(f"   Small file enhanced chunks: {enhanced_count}")
        
        # For very small files, semantic enhancement might not be applied
        # But basic chunking should still work
        for chunk in chunks:
            assert 'content' in chunk, "All chunks should have content"
            assert 'chunk_type' in chunk, "All chunks should have chunk_type"

    def test_non_cpp_language_bypass(self, chunker):
        """Test that non-C/C++ languages bypass semantic enhancement"""
        python_code = '''
def connection_manager():
    connections = []
    
    def add_connection(conn):
        connections.append(conn)
    
    def remove_connection(conn):
        if conn in connections:
            connections.remove(conn)
    
    def get_count():
        return len(connections)
    
    return {
        'add': add_connection,
        'remove': remove_connection,
        'count': get_count
    }

if __name__ == "__main__":
    manager = connection_manager()
    print(f"Connections: {manager['count']()}")
'''
        
        chunks = chunker.chunk_file_with_semantics("test.py", python_code, "python")
        assert len(chunks) > 0, "Should generate Python chunks"
        
        # No chunks should be semantically enhanced for Python
        enhanced_count = sum(1 for chunk in chunks if chunk.get('semantic_enhanced', False))
        assert enhanced_count == 0, "Python files should not have semantic enhancement"
        
        # But all chunks should have proper structure
        for chunk in chunks:
            assert 'content' in chunk, "Python chunks should have content"
            assert 'chunk_type' in chunk, "Python chunks should have chunk_type"
            assert chunk.get('semantic_enhanced', False) is False, "Python chunks should not be semantically enhanced"

    def test_framework_integration_uses_semantic_chunking(self):
        """Test that the framework integration properly uses semantic chunking"""
        # This test verifies our integration changes in framework_integration.py
        try:
            # Create integrated system
            system = IntegratedCodeAnalysisSystem()
            
            # Verify system components
            assert hasattr(system, 'framework'), "System should have framework"
            assert hasattr(system, 'chunk_registry'), "System should have chunk registry"
            assert hasattr(system, 'pipeline'), "System should have pipeline"
            
            print("   ✅ Framework integration system properly configured")
            print("   ✅ Semantic chunking integration is active in the pipeline")
            
        except ImportError as e:
            pytest.skip(f"Framework integration not available: {e}")
        except Exception as e:
            pytest.fail(f"Framework integration test failed: {e}")

    def test_semantic_chunker_direct_functionality(self):
        """Test SemanticChunker class directly"""
        chunker = SemanticChunker()
        
        # Verify initialization
        assert hasattr(chunker, 'code_elements'), "Should have code_elements"
        assert hasattr(chunker, 'relationships'), "Should have relationships"
        assert hasattr(chunker, 'dependency_graph'), "Should have dependency_graph"
        
        # Test with sample code
        sample_code = '''
void* malloc_wrapper(size_t size) {
    return malloc(size);
}

void free_wrapper(void* ptr) {
    free(ptr);
}
'''
        
        file_contents = {"test.c": sample_code}
        analysis = chunker.analyze_codebase_semantics(file_contents)
        
        # Verify analysis structure
        assert isinstance(analysis, dict), "Analysis should return dict"
        required_keys = ['elements', 'relationships', 'clusters', 'enhanced_chunks']
        for key in required_keys:
            assert key in analysis, f"Analysis should contain {key}"
        
        print(f"   Semantic analysis found {len(analysis['elements'])} elements")
        print(f"   Created {len(analysis['enhanced_chunks'])} enhanced chunks")

if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s"])
