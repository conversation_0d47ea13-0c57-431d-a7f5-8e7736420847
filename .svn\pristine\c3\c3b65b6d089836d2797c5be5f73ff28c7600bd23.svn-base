"""
Test OpenWebUI integration functionality.
Tests the integration between the code analyzer server and OpenWebUI.
"""
import pytest
import httpx
import asyncio
from typing import Dict, Any, Optional


class TestOpenWebUIIntegration:
    """Test OpenWebUI integration capabilities."""

    # OpenWebUI configuration
    OPENWEBUI_URL = "http://home-ai-server.local:8080"
    API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    # Test queries for OpenWebUI integration
    TEST_QUERIES = [
        "list codebases",
        "help me understand memory management",
        "search for error handling patterns",
        "what functions are available?",
        "show me the main components"
    ]

    @pytest.fixture
    async def openwebui_client(self):
        """Create an HTTP client for OpenWebUI testing."""
        headers = {
            "Authorization": f"Bearer {self.API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient(
            base_url=self.OPENWEBUI_URL,
            headers=headers,
            timeout=30.0
        ) as client:
            yield client

    @pytest.mark.asyncio
    async def test_openwebui_server_accessibility(self, openwebui_client: httpx.AsyncClient):
        """Test that OpenWebUI server is accessible."""
        try:
            # Try health endpoint first
            response = await openwebui_client.get("/health")
            if response.status_code == 200:
                print("✅ OpenWebUI health endpoint accessible")
                return
            
            # Try root endpoint as fallback
            response = await openwebui_client.get("/")
            if response.status_code in [200, 404]:  # 404 is acceptable for root
                print("✅ OpenWebUI server accessible")
                return
                
            pytest.skip(f"OpenWebUI server not accessible: {response.status_code}")
            
        except Exception as e:
            pytest.skip(f"OpenWebUI server not accessible: {e}")

    @pytest.mark.asyncio
    async def test_code_analyzer_tool_availability(self, http_client: httpx.AsyncClient):
        """Test that the code analyzer tool is available for OpenWebUI integration."""
        # Check if the server provides tool information
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        # The server should provide information about available tools
        assert "management_tools" in data or "optimized_tools" in data
        
        if "management_tools" in data:
            tools = data["management_tools"]
            expected_tools = ["list_codebases", "select_codebase"]
            for tool in expected_tools:
                assert tool in tools, f"Tool {tool} should be available"

    @pytest.mark.asyncio
    async def test_tool_endpoint_compatibility(self, http_client: httpx.AsyncClient):
        """Test that tool endpoints are compatible with OpenWebUI integration."""
        # Test key endpoints that OpenWebUI tool would use
        endpoints_to_test = [
            ("/tools/list_codebases", "POST"),
            ("/tools/enhanced_search", "POST"),
            ("/tools/detect_intent", "POST"),
            ("/tools/framework_status", "GET")
        ]
        
        for endpoint, method in endpoints_to_test:
            try:
                if method == "GET":
                    response = await http_client.get(endpoint)
                else:
                    # Use minimal payload for POST requests
                    payload = {"query": "test"} if "search" in endpoint or "intent" in endpoint else {}
                    response = await http_client.post(endpoint, json=payload)
                
                assert response.status_code == 200, f"Endpoint {endpoint} should be accessible"
                
                # Response should be JSON
                data = response.json()
                assert isinstance(data, dict), f"Endpoint {endpoint} should return JSON"
                
                print(f"✅ {method} {endpoint} - Compatible")
                
            except httpx.ReadTimeout:
                print(f"⏰ {method} {endpoint} - Timeout (may be expected for complex operations)")
            except Exception as e:
                print(f"⚠️ {method} {endpoint} - Error: {e}")

    @pytest.mark.asyncio
    async def test_codebase_selection_workflow(self, http_client: httpx.AsyncClient):
        """Test the codebase selection workflow that OpenWebUI tool would use."""
        # Step 1: List available codebases
        response = await http_client.post("/tools/list_codebases")
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data or "codebases" in data
        
        # Step 2: Try to select a codebase (use utils as it's commonly available)
        select_payload = {"codebase_name": "utils"}
        response = await http_client.post("/tools/select_codebase", json=select_payload)
        assert response.status_code == 200
        
        select_data = response.json()
        assert "result" in select_data
        
        print("✅ Codebase selection workflow compatible with OpenWebUI")

    @pytest.mark.asyncio
    async def test_search_workflow_compatibility(self, http_client: httpx.AsyncClient):
        """Test the search workflow that OpenWebUI tool would use."""
        # Test enhanced search with typical OpenWebUI query
        payload = {
            "codebase_name": "utils",
            "query": "memory management functions",
            "max_results": 5
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data or "error" in data
            
            if "result" in data:
                # Result should be formatted text suitable for OpenWebUI display
                result = data["result"]
                assert isinstance(result, str)
                assert len(result) > 0
                
            print("✅ Search workflow compatible with OpenWebUI")
            
        except httpx.ReadTimeout:
            pytest.skip("Search workflow test timed out - may be expected for complex queries")

    @pytest.mark.asyncio
    async def test_intent_detection_integration(self, http_client: httpx.AsyncClient):
        """Test intent detection integration for OpenWebUI queries."""
        # Test various query types that OpenWebUI users might send
        test_queries = [
            "help me understand this code",
            "list all available codebases", 
            "search for memory functions",
            "what are the main classes?"
        ]
        
        for query in test_queries:
            payload = {"query": query}
            response = await http_client.post("/tools/detect_intent", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "intent" in data
            assert "confidence" in data
            
            print(f"✅ Intent detected for: '{query}' -> {data.get('intent')}")

    @pytest.mark.asyncio
    async def test_error_handling_for_openwebui(self, http_client: httpx.AsyncClient):
        """Test error handling that OpenWebUI integration would encounter."""
        # Test with invalid codebase name
        payload = {
            "codebase_name": "nonexistent_codebase",
            "query": "test query"
        }
        
        response = await http_client.post("/tools/enhanced_search", json=payload)
        assert response.status_code == 200  # Should handle gracefully
        
        data = response.json()
        # Should return error information in a format OpenWebUI can handle
        assert "result" in data or "error" in data
        
        print("✅ Error handling compatible with OpenWebUI")

    @pytest.mark.asyncio
    async def test_response_format_compatibility(self, http_client: httpx.AsyncClient):
        """Test that response formats are compatible with OpenWebUI expectations."""
        # Test list codebases response format
        response = await http_client.post("/tools/list_codebases")
        assert response.status_code == 200
        
        data = response.json()
        if "result" in data:
            # Result should be formatted text
            result = data["result"]
            assert isinstance(result, str)
            # Should contain readable information about codebases
            assert len(result) > 50  # Should have substantial content
            
        print("✅ Response format compatible with OpenWebUI display")

    @pytest.mark.asyncio
    async def test_session_management_compatibility(self, http_client: httpx.AsyncClient):
        """Test session management compatibility with OpenWebUI."""
        # Test session codebase endpoints
        payload = {"codebase_name": "utils"}
        
        # Set session codebase
        response = await http_client.post("/tools/set_session_codebase", json=payload)
        assert response.status_code == 200
        
        # Get session codebase
        response = await http_client.get("/tools/get_session_codebase")
        assert response.status_code == 200
        
        # Clear session codebase
        response = await http_client.post("/tools/clear_session_codebase")
        assert response.status_code == 200
        
        print("✅ Session management compatible with OpenWebUI")

    @pytest.mark.asyncio
    async def test_framework_status_for_openwebui(self, http_client: httpx.AsyncClient):
        """Test framework status information for OpenWebUI display."""
        response = await http_client.get("/tools/framework_status")
        assert response.status_code == 200
        
        data = response.json()
        # Should provide comprehensive status information
        assert "framework_available" in data
        assert "supported_languages" in data
        assert "gpu_infrastructure" in data
        
        # Information should be suitable for OpenWebUI display
        assert isinstance(data["supported_languages"], int)
        assert data["supported_languages"] > 40  # Should support many languages
        
        print(f"✅ Framework status: {data['supported_languages']} languages supported")

    @pytest.mark.asyncio
    async def test_tool_configuration_compatibility(self, http_client: httpx.AsyncClient):
        """Test tool configuration compatibility with OpenWebUI."""
        # Test that the server provides tool configuration information
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        # Should provide information about available tools and features
        expected_fields = ["enhanced_features", "optimized_tools", "management_tools"]
        
        for field in expected_fields:
            if field in data:
                assert isinstance(data[field], list)
                assert len(data[field]) > 0
                print(f"✅ {field}: {len(data[field])} items available")

    @pytest.mark.asyncio
    async def test_large_response_handling(self, http_client: httpx.AsyncClient):
        """Test handling of large responses that OpenWebUI might receive."""
        # Test with a broad query that might return large results
        payload = {
            "codebase_name": "utils",
            "query": "functions",  # Broad query
            "max_results": 10
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            if "result" in data:
                result = data["result"]
                # Should handle large responses appropriately
                assert isinstance(result, str)
                # Should not be excessively large (OpenWebUI has limits)
                assert len(result) < 50000  # Reasonable size limit
                
            print("✅ Large response handling compatible with OpenWebUI")
            
        except httpx.ReadTimeout:
            pytest.skip("Large response test timed out - may be expected behavior")
