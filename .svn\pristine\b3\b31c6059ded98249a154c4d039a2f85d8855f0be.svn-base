# User Guide

Comprehensive guide for using the OpenWebUI RAG Code Server, covering all features, API endpoints, and usage examples.

## 🎯 Overview

The OpenWebUI RAG Code Server provides multiple interfaces for code analysis:

1. **OpenWebUI Tool**: Natural language interface through chat
2. **REST API**: Programmatic access for automation
3. **Web Dashboard**: Visual management and monitoring
4. **Command Line**: Direct Python API usage

## 🚀 Getting Started

### 1. First Steps

After installation, start by processing your first codebase:

```bash
# Using OpenWebUI Tool
"process codebase my_project"

# Using REST API
curl -X POST http://localhost:5002/tools/process_codebase \
  -H "Content-Type: application/json" \
  -d '{"codebase_name": "my_project"}'
```

### 2. Basic Workflow

1. **List Available Codebases**: See what's in your source_code directory
2. **Process Codebase**: Index and analyze the code
3. **Select Codebase**: Set the active codebase for queries
4. **Search and Analyze**: Find code and get AI insights

## 🤖 OpenWebUI Tool Usage

### Basic Commands

#### Codebase Management
```bash
# List all available codebases
"list codebases"

# Process a new codebase
"process codebase utils"

# Select active codebase
"select codebase utils"

# Get codebase statistics
"get stats for utils"

# Delete a codebase (removes index, keeps source)
"delete codebase old_project"
```

#### Code Search
```bash
# Basic search
"search code memory allocation"

# Language-specific search
"search code database connection in python"

# Advanced search with filters
"search code REST API in csharp with 15 results"

# Search specific file types
"search code error handling in .cpp files"
```

#### AI Analysis
```bash
# General code questions
"ask about code: How does authentication work?"

# Architecture analysis
"ask about code: What design patterns are used?"

# Cross-language analysis
"ask about code: How do the Python and C# services communicate?"

# Performance analysis
"ask about code: What are the performance bottlenecks?"
```

#### System Status
```bash
# Quick status check
"status"

# Detailed server status
"get server status"

# Health check
"health check"

# Help and available commands
"help"
```

### Advanced Features

#### 🧠 Semantic Analysis (Enhanced for C/C++)
The system provides advanced semantic analysis for C/C++ codebases, offering richer context and better search results:

```bash
# Enhanced semantic search
"explain tmwmem_lowFree function"
# Returns: Function + related structures + configuration + usage examples

# Memory management analysis
"find memory allocation functions"
# Returns: Semantic clusters with related functions, data structures, and patterns
```

**Semantic Features:**
- **Context Grouping**: Functions shown with related structures and dependencies
- **Documentation Integration**: Includes function comments and documentation
- **Usage Examples**: Shows how functions are used in the codebase
- **Configuration Context**: Includes relevant configuration macros and settings

> 📖 **Learn More**: See [SEMANTIC_ANALYSIS_USER_GUIDE.md](SEMANTIC_ANALYSIS_USER_GUIDE.md) for detailed semantic analysis capabilities.

#### Language Filtering
```bash
# Search specific languages
"search code memory management in cpp"
"search code async patterns in python"
"search code LINQ queries in csharp"
"search code promises in javascript"
```

#### Result Customization
```bash
# Control number of results
"search code database with 20 results"

# Verbose output with metadata
"search code authentication with verbose output"

# Category-specific search
"search code security patterns"
"search code API endpoints"
```

#### Batch Operations
```bash
# Process multiple codebases
"process codebases utils, networking_project, z80emu"

# Compare across codebases
"compare error handling across all codebases"
```

## 🌐 REST API Reference

### Base URL
```
http://localhost:5002
```

### Authentication
Currently no authentication required for local deployment. For production, implement API key authentication.

### Core Endpoints

#### Health and Status
```bash
# Health check
GET /health

# Detailed health information
GET /health/detailed

# Analysis service health
GET /analysis/health

# System status
GET /
```

#### Codebase Management
```bash
# List codebases
POST /tools/list_codebases
Content-Type: application/json
{}

# Process codebase
POST /tools/process_codebase
Content-Type: application/json
{
  "codebase_name": "my_project",
  "exclude_dirs": ["build", "test", "__pycache__"]
}

# Select codebase
POST /tools/select_codebase
Content-Type: application/json
{
  "codebase_name": "my_project"
}

# Get statistics
POST /tools/get_code_stats
Content-Type: application/json
{
  "codebase_name": "my_project"
}

# Delete codebase
POST /tools/delete_codebase
Content-Type: application/json
{
  "codebase_name": "old_project"
}
```

#### Code Search
```bash
# Search code
POST /tools/search_code
Content-Type: application/json
{
  "query": "memory allocation",
  "codebase_name": "utils",
  "filter_language": "cpp",
  "n_results": 10,
  "include_metadata": true
}

# Enhanced search with context
POST /tools/enhanced_search
Content-Type: application/json
{
  "query": "database connection",
  "codebase_name": "my_project",
  "n_results": 20,
  "include_metadata": true
}
```

#### AI Analysis
```bash
# Ask about code
POST /tools/ask_about_code
Content-Type: application/json
{
  "question": "How does error handling work?",
  "codebase_name": "my_project",
  "context_size": 5
}

# Get optimized context
POST /tools/get_optimized_context
Content-Type: application/json
{
  "query": "Explain the architecture",
  "codebase_name": "my_project",
  "n_results": 10
}
```

### Response Formats

#### Success Response
```json
{
  "success": true,
  "data": {
    "results": [...],
    "metadata": {...}
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "CODEBASE_NOT_FOUND",
    "message": "Codebase 'invalid' not found",
    "details": {...}
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

## 🖥️ Web Dashboard Usage

### Accessing the Dashboard
```
http://localhost:5003
```

### Dashboard Features

#### Server Health Panel
- Real-time server status
- Component health indicators
- Performance metrics
- System validation status

#### Codebase Management Panel
- List all available codebases
- View processing status
- Codebase statistics
- Quick actions (select, delete, reprocess)

#### GPU Infrastructure Panel
- GPU availability and specifications
- Hardware utilization
- Performance monitoring

#### Query Testing Panel
- Interactive query interface
- Real-time results display
- Performance timing
- Error handling

### Dashboard Operations

#### Codebase Operations
1. **View Codebases**: See all available codebases with status
2. **Select Codebase**: Click to set as active
3. **Process New**: Add and process new codebases
4. **View Statistics**: Detailed metrics and information

#### Testing and Debugging
1. **Test Queries**: Interactive query testing
2. **View Logs**: Real-time log monitoring
3. **Performance Metrics**: Response time analysis
4. **Health Monitoring**: System component status

## 📊 Language-Specific Features

### C/C++ Analysis (with Semantic Enhancement)
```bash
# Memory management patterns with semantic context
"search code malloc free in cpp"

# Enhanced semantic analysis for C/C++
"explain memory allocation in utils codebase"
# Returns rich context including:
# - Function implementations
# - Related data structures
# - Configuration dependencies
# - Usage patterns and examples

# Header-implementation relationships
"search code function declarations in .h files"

# Template usage
"search code template specialization"
```

### Python Analysis
```bash
# Async patterns
"search code async def await"

# Import relationships
"search code import statements"

# Class hierarchies
"search code class inheritance"
```

### C# Analysis
```bash
# LINQ patterns
"search code LINQ queries"

# Async/await patterns
"search code async Task"

# Dependency injection
"search code dependency injection"
```

### JavaScript/TypeScript Analysis
```bash
# Promise patterns
"search code Promise async"

# Module imports
"search code import export"

# React components
"search code React component"
```

## 🔍 Advanced Search Techniques

### Search Operators
```bash
# Exact phrase matching
"search code \"memory allocation\""

# Multiple terms
"search code database AND connection"

# Exclude terms
"search code authentication NOT deprecated"
```

### Metadata Filtering
```bash
# File type filtering
"search code error handling in .py files"

# Directory filtering
"search code API in src/ directory"

# Size filtering
"search code large functions over 100 lines"
```

### Semantic Categories
```bash
# Domain-specific searches
"search code security patterns"
"search code performance optimizations"
"search code error handling"
"search code API endpoints"
"search code database operations"
```

## 🎯 Use Case Examples

### Code Discovery
```bash
# Find all authentication mechanisms
"search code authentication login password"

# Discover API endpoints
"search code REST API endpoint route"

# Find configuration patterns
"search code configuration settings"
```

### Architecture Analysis
```bash
# Understand system design
"ask about code: What is the overall architecture?"

# Find design patterns
"search code singleton factory observer"

# Analyze dependencies
"ask about code: What are the main dependencies?"
```

### Code Quality Assessment
```bash
# Find potential issues
"search code TODO FIXME BUG"

# Security analysis
"search code SQL injection XSS CSRF"

# Performance analysis
"search code performance bottleneck slow"
```

### Migration Planning
```bash
# Find legacy code
"search code deprecated legacy old"

# Cross-language comparison
"ask about code: Compare Python and C# implementations"

# Modernization opportunities
"search code outdated patterns"
```

## 📈 Performance Tips

### Efficient Searching
1. **Use specific terms**: "database connection" vs "database"
2. **Apply language filters**: Narrow scope for faster results
3. **Limit result count**: Use appropriate n_results parameter
4. **Cache frequent queries**: Results are cached for performance

### Optimal Codebase Processing
1. **Exclude unnecessary directories**: build/, test/, node_modules/
2. **Process incrementally**: Start with core modules
3. **Monitor resource usage**: Check memory and CPU usage
4. **Regular maintenance**: Reprocess when code changes significantly

### Best Practices
1. **Organize source code**: Clear directory structure
2. **Use descriptive names**: Clear codebase naming
3. **Regular updates**: Reprocess after major changes
4. **Monitor performance**: Use web dashboard metrics

## 🔧 Configuration Options

### Search Configuration
```python
# Default search parameters
DEFAULT_MAX_RESULTS = 10
DEFAULT_CONTEXT_SIZE = 5
DEFAULT_SIMILARITY_THRESHOLD = 0.7
```

### Processing Configuration
```python
# Codebase processing options
EXCLUDE_DIRS = ["build", "test", "__pycache__", "node_modules"]
CHUNK_SIZE = 1000
OVERLAP_SIZE = 200
```

### AI Configuration
```python
# Ollama integration settings
OLLAMA_MODEL = "llama3:latest"
CONTEXT_WINDOW = 4096
TEMPERATURE = 0.1
```

## 🆘 Common Issues and Solutions

### Search Returns No Results
1. **Check codebase selection**: Ensure correct codebase is selected
2. **Verify processing**: Confirm codebase is fully processed
3. **Broaden search terms**: Use more general keywords
4. **Check language filters**: Remove or adjust language restrictions

### Slow Performance
1. **Reduce result count**: Lower n_results parameter
2. **Use language filters**: Narrow search scope
3. **Check system resources**: Monitor CPU and memory usage
4. **Optimize queries**: Use more specific search terms

### AI Analysis Issues
1. **Check Ollama connection**: Verify AI service is running
2. **Increase context size**: Provide more context for analysis
3. **Simplify questions**: Break complex questions into parts
4. **Check model availability**: Ensure required models are installed

For more detailed troubleshooting, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).
