{".class": "MypyFile", "_fullname": "semantic_chunking_enhancement", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CodeElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.CodeElement", "name": "CodeElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.CodeElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "element_type", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "content", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "file_path", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "start_line", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "end_line", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "documentation", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "dependencies", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "usage_examples", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "configuration_deps", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "semantic_tags", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.CodeElement", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "element_type", "content", "file_path", "start_line", "end_line", "documentation", "dependencies", "usage_examples", "configuration_deps", "semantic_tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.CodeElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "element_type", "content", "file_path", "start_line", "end_line", "documentation", "dependencies", "usage_examples", "configuration_deps", "semantic_tags"], "arg_types": ["semantic_chunking_enhancement.CodeElement", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CodeElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "semantic_chunking_enhancement.CodeElement.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "element_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "content"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "file_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "start_line"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "end_line"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "documentation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dependencies"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "usage_examples"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "configuration_deps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "semantic_tags"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "element_type", "content", "file_path", "start_line", "end_line", "documentation", "dependencies", "usage_examples", "configuration_deps", "semantic_tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "semantic_chunking_enhancement.CodeElement.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "element_type", "content", "file_path", "start_line", "end_line", "documentation", "dependencies", "usage_examples", "configuration_deps", "semantic_tags"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CodeElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "element_type", "content", "file_path", "start_line", "end_line", "documentation", "dependencies", "usage_examples", "configuration_deps", "semantic_tags"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CodeElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "configuration_deps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.CodeElement.configuration_deps", "name": "configuration_deps", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.content", "name": "content", "type": "builtins.str"}}, "dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.CodeElement.dependencies", "name": "dependencies", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "documentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.CodeElement.documentation", "name": "documentation", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "element_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.element_type", "name": "element_type", "type": "builtins.str"}}, "end_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.end_line", "name": "end_line", "type": "builtins.int"}}, "file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.file_path", "name": "file_path", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.name", "name": "name", "type": "builtins.str"}}, "semantic_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.CodeElement.semantic_tags", "name": "semantic_tags", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "start_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.CodeElement.start_line", "name": "start_line", "type": "builtins.int"}}, "usage_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.CodeElement.usage_examples", "name": "usage_examples", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.CodeElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.CodeElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContextAwareRanker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.ContextAwareRanker", "name": "ContextAwareRanker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.ContextAwareRanker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.ContextAwareRanker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.ContextAwareRanker.__init__", "name": "__init__", "type": null}}, "_calculate_result_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "query_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.ContextAwareRanker._calculate_result_score", "name": "_calculate_result_score", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "query_context"], "arg_types": ["semantic_chunking_enhancement.ContextAwareRanker", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_result_score of ContextAwareRanker", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ranking_factors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.ContextAwareRanker._get_ranking_factors", "name": "_get_ranking_factors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["semantic_chunking_enhancement.ContextAwareRanker", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ranking_factors of ContextAwareRanker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rank_search_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "results", "query_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.ContextAwareRanker.rank_search_results", "name": "rank_search_results", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "results", "query_context"], "arg_types": ["semantic_chunking_enhancement.ContextAwareRanker", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rank_search_results of ContextAwareRanker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ranking_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "semantic_chunking_enhancement.ContextAwareRanker.ranking_weights", "name": "ranking_weights", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.ContextAwareRanker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.ContextAwareRanker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContextLevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.ContextLevel", "name": "ContextLevel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "semantic_chunking_enhancement.ContextLevel", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.ContextLevel", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CONFIGURATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.ContextLevel.CONFIGURATION", "name": "CONFIGURATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SIGNATURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.ContextLevel.SIGNATURE", "name": "SIGNATURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "STRUCTURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.ContextLevel.STRUCTURE", "name": "STRUCTURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.ContextLevel.USAGE", "name": "USAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.ContextLevel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.ContextLevel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EnhancedQueryProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor", "name": "EnhancedQueryProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "semantic_chunker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "semantic_chunker"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "semantic_chunking_enhancement.SemanticChunker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EnhancedQueryProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_configuration_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._build_configuration_context", "name": "_build_configuration_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "semantic_chunking_enhancement.CodeElement", {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_configuration_context of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_dependency_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._build_dependency_context", "name": "_build_dependency_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "semantic_chunking_enhancement.CodeElement", {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_dependency_context of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_explanation_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._build_explanation_context", "name": "_build_explanation_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "semantic_chunking_enhancement.CodeElement", {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_explanation_context of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_usage_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._build_usage_context", "name": "_build_usage_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "codebase_elements"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "semantic_chunking_enhancement.CodeElement", {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_usage_context of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_enhanced_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "original_query", "target_element", "context_expansion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._create_enhanced_query", "name": "_create_enhanced_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "original_query", "target_element", "context_expansion"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_enhanced_query of EnhancedQueryProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detect_query_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._detect_query_type", "name": "_detect_query_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_query_type of EnhancedQueryProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_search_terms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context_expansion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor._extract_search_terms", "name": "_extract_search_terms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context_expansion"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_search_terms of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enhance_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "codebase_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor.enhance_query", "name": "enhance_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "codebase_elements"], "arg_types": ["semantic_chunking_enhancement.EnhancedQueryProcessor", "builtins.str", {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enhance_query of EnhancedQueryProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor.query_patterns", "name": "query_patterns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "semantic_chunker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor.semantic_chunker", "name": "semantic_chunker", "type": "semantic_chunking_enhancement.SemanticChunker"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.EnhancedQueryProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.EnhancedQueryProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SemanticChunker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.SemanticChunker", "name": "SemanticChunker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.SemanticChunker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker.__init__", "name": "__init__", "type": null}}, "_build_dependency_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._build_dependency_graph", "name": "_build_dependency_graph", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_dependency_graph of SemanticChunker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_cluster_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._create_cluster_chunk", "name": "_create_cluster_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_cluster_chunk of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_individual_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._create_individual_chunk", "name": "_create_individual_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "semantic_chunking_enhancement.CodeElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_individual_chunk of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_semantic_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clusters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._create_semantic_chunks", "name": "_create_semantic_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "clusters"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_semantic_chunks of SemanticChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_cluster_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster_elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._determine_cluster_type", "name": "_determine_cluster_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster_elements"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_cluster_type of SemanticChunker", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_code_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_code_elements", "name": "_extract_code_elements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_code_elements of SemanticChunker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_data_structures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_data_structures", "name": "_extract_data_structures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_data_structures of SemanticChunker", "ret_type": {".class": "Instance", "args": ["semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_function_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "start_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_function_body", "name": "_extract_function_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "start_pos"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_function_body of SemanticChunker", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_function_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_function_dependencies", "name": "_extract_function_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func_content"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_function_dependencies of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_functions_with_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_functions_with_docs", "name": "_extract_functions_with_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_functions_with_docs of SemanticChunker", "ret_type": {".class": "Instance", "args": ["semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_macros_and_constants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_macros_and_constants", "name": "_extract_macros_and_constants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "content"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_macros_and_constants of SemanticChunker", "ret_type": {".class": "Instance", "args": ["semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_struct_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "struct_body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._extract_struct_dependencies", "name": "_extract_struct_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "struct_body"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_struct_dependencies of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_related_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_element", "visited"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._find_related_elements", "name": "_find_related_elements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_element", "visited"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_related_elements of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_semantic_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "func_name", "func_content", "doc_comment", "return_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._generate_semantic_tags", "name": "_generate_semantic_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "func_name", "func_content", "doc_comment", "return_type"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_semantic_tags of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_semantic_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker._identify_semantic_clusters", "name": "_identify_semantic_clusters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_semantic_clusters of SemanticChunker", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_codebase_semantics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticChunker.analyze_codebase_semantics", "name": "analyze_codebase_semantics", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_contents"], "arg_types": ["semantic_chunking_enhancement.SemanticChunker", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_codebase_semantics of SemanticChunker", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_elements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "semantic_chunking_enhancement.SemanticChunker.code_elements", "name": "code_elements", "type": {".class": "Instance", "args": ["builtins.str", "semantic_chunking_enhancement.CodeElement"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "dependency_graph": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "semantic_chunking_enhancement.SemanticChunker.dependency_graph", "name": "dependency_graph", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "relationships": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "semantic_chunking_enhancement.SemanticChunker.relationships", "name": "relationships", "type": {".class": "Instance", "args": ["semantic_chunking_enhancement.SemanticRelationship"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.SemanticChunker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.SemanticChunker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SemanticRelationship": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "semantic_chunking_enhancement.SemanticRelationship", "name": "SemanticRelationship", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticRelationship", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "source_element", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "target_element", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "relationship_type", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "strength", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "context", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "semantic_chunking_enhancement", "mro": ["semantic_chunking_enhancement.SemanticRelationship", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "source_element", "target_element", "relationship_type", "strength", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.SemanticRelationship.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "source_element", "target_element", "relationship_type", "strength", "context"], "arg_types": ["semantic_chunking_enhancement.SemanticRelationship", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SemanticRelationship", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "source_element"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "target_element"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "relationship_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strength"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "context"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["source_element", "target_element", "relationship_type", "strength", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["source_element", "target_element", "relationship_type", "strength", "context"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SemanticRelationship", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["source_element", "target_element", "relationship_type", "strength", "context"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of SemanticRelationship", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.context", "name": "context", "type": "builtins.str"}}, "relationship_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.relationship_type", "name": "relationship_type", "type": "builtins.str"}}, "source_element": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.source_element", "name": "source_element", "type": "builtins.str"}}, "strength": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.strength", "name": "strength", "type": "builtins.float"}}, "target_element": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "semantic_chunking_enhancement.SemanticRelationship.target_element", "name": "target_element", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "semantic_chunking_enhancement.SemanticRelationship.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "semantic_chunking_enhancement.SemanticRelationship", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "semantic_chunking_enhancement.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "analysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.analysis", "name": "analysis", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chunker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.chunker", "name": "chunker", "type": "semantic_chunking_enhancement.SemanticChunker"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "enhanced": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.enhanced", "name": "enhanced", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "integrate_semantic_chunking": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["existing_chunker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "semantic_chunking_enhancement.integrate_semantic_chunking", "name": "integrate_semantic_chunking", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "query_processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.query_processor", "name": "query_processor", "type": "semantic_chunking_enhancement.EnhancedQueryProcessor"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sample_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "semantic_chunking_enhancement.sample_code", "name": "sample_code", "type": "builtins.str"}}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\semantic_chunking_enhancement.py"}