"""
Test data quality and hallucination detection.
Tests that the server returns real code content instead of hallucinated examples.
"""
import pytest
import httpx
import asyncio
import re


class TestDataQuality:
    """Test data quality and hallucination detection."""

    # Hallucination indicators (patterns that suggest generated/fake code)
    HALLUCINATION_INDICATORS = [
        "Here's an example",
        "You can use these functions",
        "This is a basic example",
        "def example_function",
        "# Example usage",
        "import example",
        "class ExampleClass",
        "def sample_function",
        "# Sample code",
        "def demo_function"
    ]

    # Real code indicators for utils codebase (from final_test_memory_management.py)
    UTILS_REAL_CODE_INDICATORS = [
        "tmwmem",
        "Triangle MicroWorks",
        "/app/source_code/utils",
        "Copyright (c) 1997-2023",
        "TMWMEM_ALLOC",
        "tmwmem_alloc",
        "tmwmem_free",
        "TMWMEM_FREE",
        "tmwmem.h",
        "tmwmem.c"
    ]

    # Generic real code indicators
    GENERIC_REAL_CODE_INDICATORS = [
        "Copyright",
        "License",
        "#include",
        "static ",
        "extern ",
        "typedef ",
        "struct ",
        "enum ",
        "const ",
        "void *",
        "int main",
        "return 0"
    ]

    @pytest.mark.asyncio
    async def test_memory_management_query_authenticity(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that memory management queries return real code, not hallucinated examples."""
        payload = {
            "codebase_name": "utils",
            "query": "show memory management in utils codebase",
            "max_results": 5
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data
            
            result = data["result"]
            assert isinstance(result, str)
            assert len(result) > 100  # Should have substantial content
            
            # Check for hallucination indicators
            found_hallucination = [ind for ind in self.HALLUCINATION_INDICATORS if ind.lower() in result.lower()]
            found_real_code = [ind for ind in self.UTILS_REAL_CODE_INDICATORS if ind in result]
            
            # Should have more real code indicators than hallucination indicators
            print(f"✅ Hallucination indicators found: {len(found_hallucination)}")
            print(f"✅ Real code indicators found: {len(found_real_code)}")
            
            if found_real_code:
                # If we found real code indicators, hallucination should be minimal
                assert len(found_hallucination) <= len(found_real_code), \
                    f"Too many hallucination indicators: {found_hallucination}"
            
        except httpx.ReadTimeout:
            pytest.skip("Memory management query timed out - may be expected for complex queries")

    @pytest.mark.asyncio
    async def test_code_authenticity_across_codebases(self, http_client: httpx.AsyncClient):
        """Test code authenticity across different codebases."""
        test_cases = [
            ("utils", "memory allocation"),
            ("modbus", "modbus protocol"),
            ("z80emu", "z80 emulation"),
            ("go-example-master", "go functions")
        ]
        
        for codebase, query in test_cases:
            payload = {
                "codebase_name": codebase,
                "query": query,
                "max_results": 3
            }
            
            try:
                response = await http_client.post("/tools/enhanced_search", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if "result" in data:
                        result = data["result"]
                        
                        # Check for generic hallucination patterns
                        hallucination_count = sum(1 for ind in self.HALLUCINATION_INDICATORS 
                                                if ind.lower() in result.lower())
                        
                        # Should not have excessive hallucination indicators
                        assert hallucination_count <= 2, \
                            f"Too many hallucination indicators in {codebase}: {hallucination_count}"
                        
                        print(f"✅ {codebase}: {hallucination_count} hallucination indicators")
                        
            except httpx.ReadTimeout:
                print(f"⏰ {codebase} query timed out")

    @pytest.mark.asyncio
    async def test_metadata_quality_indicators(self, http_client: httpx.AsyncClient):
        """Test that responses include quality metadata indicators."""
        payload = {
            "codebase_name": "utils",
            "query": "memory functions",
            "max_results": 3
        }
        
        try:
            response = await http_client.post("/tools/get_optimized_context", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data
            
            result = data["result"]
            
            # Check for enhanced metadata indicators
            metadata_indicators = [
                'enhanced', 'semantic_tags', 'quality_score', 'chunk_id',
                'complexity_metrics', 'quality_indicators', 'code_patterns',
                'framework_processed', 'query_relevance'
            ]
            
            found_indicators = [ind for ind in metadata_indicators if ind in result.lower()]
            
            # Should have some metadata indicators in enhanced responses
            assert len(found_indicators) > 0, "No metadata indicators found in enhanced response"
            
            print(f"✅ Metadata indicators found: {found_indicators}")
            
        except httpx.ReadTimeout:
            pytest.skip("Metadata quality test timed out")

    @pytest.mark.asyncio
    async def test_response_consistency(self, http_client: httpx.AsyncClient):
        """Test that repeated queries return consistent, non-random results."""
        query = "memory management"
        payload = {
            "codebase_name": "utils",
            "query": query,
            "max_results": 3
        }
        
        responses = []
        
        # Make multiple requests
        for i in range(2):  # Conservative for real server testing
            try:
                response = await http_client.post("/tools/enhanced_search", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if "result" in data:
                        responses.append(data["result"])
                
                # Small delay between requests
                await asyncio.sleep(1)
                
            except httpx.ReadTimeout:
                print(f"Request {i+1} timed out")
        
        if len(responses) >= 2:
            # Responses should have some consistency (not completely random)
            # Check for common keywords/patterns
            common_words = set()
            for response in responses:
                words = set(re.findall(r'\b\w{4,}\b', response.lower()))
                if not common_words:
                    common_words = words
                else:
                    common_words &= words
            
            # Should have some common words across responses
            assert len(common_words) > 5, "Responses appear to be completely random"
            
            print(f"✅ Found {len(common_words)} common words across responses")

    @pytest.mark.asyncio
    async def test_source_attribution(self, http_client: httpx.AsyncClient):
        """Test that responses properly attribute source files."""
        payload = {
            "codebase_name": "utils",
            "query": "memory allocation functions",
            "max_results": 5
        }
        
        try:
            response = await http_client.post("/tools/get_optimized_context", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data
            
            result = data["result"]
            
            # Should contain source file references
            source_indicators = [
                ".c", ".h", ".cpp", ".py", ".go", ".rs", ".java",
                "source_files", "file:", "path:", "/app/source_code"
            ]
            
            found_sources = [ind for ind in source_indicators if ind in result.lower()]
            
            # Should reference actual source files
            assert len(found_sources) > 0, "No source file attribution found"
            
            print(f"✅ Source attribution indicators: {found_sources}")
            
        except httpx.ReadTimeout:
            pytest.skip("Source attribution test timed out")

    @pytest.mark.asyncio
    async def test_code_snippet_authenticity(self, http_client: httpx.AsyncClient):
        """Test that code snippets are authentic, not generated examples."""
        payload = {
            "codebase_name": "utils",
            "query": "show actual code for memory allocation",
            "max_results": 3
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data
            
            result = data["result"]
            
            # Check for authentic code patterns vs generated examples
            authentic_patterns = [
                r'#include\s*[<"][^>"]+[>"]',  # Real includes
                r'static\s+\w+',  # Static declarations
                r'typedef\s+\w+',  # Typedefs
                r'/\*.*?\*/',  # C-style comments
                r'//.*',  # C++ style comments
                r'Copyright.*\d{4}',  # Copyright notices
            ]
            
            authentic_matches = 0
            for pattern in authentic_patterns:
                if re.search(pattern, result, re.IGNORECASE | re.DOTALL):
                    authentic_matches += 1
            
            # Should have some authentic code patterns
            if len(result) > 200:  # Only check if we have substantial content
                assert authentic_matches > 0, "No authentic code patterns found"
                
            print(f"✅ Authentic code patterns found: {authentic_matches}")
            
        except httpx.ReadTimeout:
            pytest.skip("Code authenticity test timed out")

    @pytest.mark.asyncio
    async def test_enhanced_vs_legacy_quality(self, http_client: httpx.AsyncClient):
        """Test that enhanced endpoints provide better quality than legacy ones."""
        query = "memory management"
        codebase = "utils"
        
        # Test enhanced endpoint
        enhanced_payload = {
            "codebase_name": codebase,
            "query": query,
            "max_results": 3
        }
        
        # Test legacy endpoint
        legacy_payload = {
            "codebase_name": codebase,
            "query": query
        }
        
        try:
            # Get enhanced response
            enhanced_response = await http_client.post("/tools/enhanced_search", json=enhanced_payload)
            legacy_response = await http_client.post("/tools/search_code", json=legacy_payload)
            
            if enhanced_response.status_code == 200 and legacy_response.status_code == 200:
                enhanced_data = enhanced_response.json()
                legacy_data = legacy_response.json()
                
                if "result" in enhanced_data and "result" in legacy_data:
                    enhanced_result = enhanced_data["result"]
                    legacy_result = legacy_data["result"]
                    
                    # Enhanced should generally be longer and more detailed
                    print(f"✅ Enhanced response length: {len(enhanced_result)}")
                    print(f"✅ Legacy response length: {len(legacy_result)}")
                    
                    # Enhanced should have metadata indicators
                    enhanced_metadata = sum(1 for ind in ['enhanced', 'quality', 'framework'] 
                                          if ind in enhanced_result.lower())
                    legacy_metadata = sum(1 for ind in ['enhanced', 'quality', 'framework'] 
                                        if ind in legacy_result.lower())
                    
                    assert enhanced_metadata >= legacy_metadata, \
                        "Enhanced endpoint should have more metadata indicators"
                        
        except httpx.ReadTimeout:
            pytest.skip("Quality comparison test timed out")
