#!/usr/bin/env python3
"""
Test script for enhanced search ranking
Tests the improvements to search relevance and ranking
"""

import requests
import json
import time
from typing import Dict, List, Any

class SearchRankingTester:
    """Test enhanced search ranking functionality"""
    
    def __init__(self, base_url: str = "http://home-ai-server.local:5002"):
        self.base_url = base_url
        self.test_results = []
    
    def test_memory_management_search(self):
        """Test memory management specific search"""
        print("🧪 Testing memory management search...")
        
        query = "memory_management"
        codebase = "utils"
        
        try:
            response = requests.post(f"{self.base_url}/tools/search", json={
                "query": query,
                "codebase_name": codebase,
                "n_results": 5
            }, timeout=30)
            
            if response.status_code == 200:
                results = response.json()
                
                # Analyze results
                analysis = self._analyze_search_results(results, query)
                
                print(f"✅ Memory management search completed")
                print(f"   Results: {len(results)}")
                print(f"   Function implementations: {analysis['function_implementations']}")
                print(f"   Architectural overviews: {analysis['architectural_overviews']}")
                print(f"   Specific functions found: {analysis['specific_functions']}")
                
                # Check if we got better results (specific functions vs architectural overviews)
                success = analysis['function_implementations'] > analysis['architectural_overviews']
                
                self.test_results.append({
                    'test': 'memory_management_search',
                    'success': success,
                    'details': analysis,
                    'query': query,
                    'results_count': len(results)
                })
                
                return success
            else:
                print(f"❌ Search failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    def test_specific_function_search(self):
        """Test search for specific function"""
        print("🧪 Testing specific function search...")
        
        query = "tmwmem_alloc"
        codebase = "utils"
        
        try:
            response = requests.post(f"{self.base_url}/tools/search", json={
                "query": query,
                "codebase_name": codebase,
                "n_results": 5
            }, timeout=30)
            
            if response.status_code == 200:
                results = response.json()
                
                # Check if first result contains the actual function implementation
                if results:
                    first_result = results[0]
                    content = first_result.get('content', '').lower()
                    
                    # Look for function definition/implementation
                    has_function_def = 'tmwmem_alloc' in content and ('(' in content or 'function' in content)
                    is_implementation = '{' in content or 'return' in content
                    
                    print(f"✅ Function search completed")
                    print(f"   Results: {len(results)}")
                    print(f"   First result has function: {has_function_def}")
                    print(f"   First result is implementation: {is_implementation}")
                    
                    success = has_function_def and is_implementation
                    
                    self.test_results.append({
                        'test': 'specific_function_search',
                        'success': success,
                        'query': query,
                        'results_count': len(results),
                        'has_function_def': has_function_def,
                        'is_implementation': is_implementation
                    })
                    
                    return success
                else:
                    print("❌ No results returned")
                    return False
            else:
                print(f"❌ Search failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    def test_ranking_configuration(self):
        """Test ranking configuration endpoint"""
        print("🧪 Testing ranking configuration...")
        
        try:
            # Get current configuration
            response = requests.get(f"{self.base_url}/api/search/ranking_status", timeout=10)
            
            if response.status_code == 200:
                config = response.json()
                print(f"✅ Ranking configuration retrieved")
                print(f"   Enabled: {config.get('enabled', 'unknown')}")
                print(f"   Weights configured: {len(config.get('ranking_weights', {}))}")
                print(f"   Penalties configured: {len(config.get('ranking_penalties', {}))}")
                
                self.test_results.append({
                    'test': 'ranking_configuration',
                    'success': True,
                    'config': config
                })
                
                return True
            else:
                print(f"❌ Configuration endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False
    
    def _analyze_search_results(self, results: List[Dict[str, Any]], query: str) -> Dict[str, Any]:
        """Analyze search results to categorize them"""
        analysis = {
            'function_implementations': 0,
            'architectural_overviews': 0,
            'specific_functions': [],
            'languages': set(),
            'chunk_types': set()
        }
        
        for result in results:
            content = result.get('content', '').lower()
            metadata = result.get('metadata', {})
            
            # Check language
            language = str(metadata.get('language', '')).lower()
            analysis['languages'].add(language)
            
            # Check chunk type
            chunk_type = str(metadata.get('chunk_type', '')).lower()
            analysis['chunk_types'].add(chunk_type)
            
            # Categorize result type
            if language == 'multi' or 'architectural' in chunk_type:
                analysis['architectural_overviews'] += 1
            elif self._is_function_implementation(content):
                analysis['function_implementations'] += 1
                
                # Extract function names
                import re
                functions = re.findall(r'\b(\w+)\s*\(', content)
                analysis['specific_functions'].extend(functions[:3])  # Limit to first 3
        
        # Convert sets to lists for JSON serialization
        analysis['languages'] = list(analysis['languages'])
        analysis['chunk_types'] = list(analysis['chunk_types'])
        analysis['specific_functions'] = list(set(analysis['specific_functions']))  # Remove duplicates
        
        return analysis
    
    def _is_function_implementation(self, content: str) -> bool:
        """Check if content appears to be function implementation"""
        implementation_indicators = [
            '{',  # Code blocks
            'return',  # Return statements
            'if (',  # Conditionals
            'for (',  # Loops
            '= ',  # Assignments
        ]
        
        return any(indicator in content for indicator in implementation_indicators)
    
    def run_all_tests(self):
        """Run all ranking tests"""
        print("🚀 Starting Enhanced Search Ranking Tests")
        print("=" * 50)
        
        tests = [
            self.test_ranking_configuration,
            self.test_memory_management_search,
            self.test_specific_function_search
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                print()  # Add spacing between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
                print()
        
        print("=" * 50)
        print(f"🏁 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("✅ All tests passed! Enhanced ranking is working correctly.")
        else:
            print("⚠️ Some tests failed. Check the output above for details.")
        
        return passed == total
    
    def generate_report(self):
        """Generate detailed test report"""
        print("\n📊 Detailed Test Report")
        print("=" * 50)
        
        for result in self.test_results:
            print(f"Test: {result['test']}")
            print(f"Status: {'✅ PASS' if result['success'] else '❌ FAIL'}")
            
            if 'query' in result:
                print(f"Query: {result['query']}")
            if 'results_count' in result:
                print(f"Results: {result['results_count']}")
            if 'details' in result:
                details = result['details']
                print(f"Function implementations: {details.get('function_implementations', 0)}")
                print(f"Architectural overviews: {details.get('architectural_overviews', 0)}")
                if details.get('specific_functions'):
                    print(f"Functions found: {', '.join(details['specific_functions'][:5])}")
            
            print("-" * 30)

def main():
    """Main test function"""
    tester = SearchRankingTester()
    
    print("Enhanced Search Ranking Test Suite")
    print("Testing improvements to search relevance and ranking")
    print()
    
    success = tester.run_all_tests()
    tester.generate_report()
    
    if success:
        print("\n🎉 All tests passed! The enhanced ranking system is working correctly.")
        print("Memory management queries should now return specific function implementations")
        print("instead of broad architectural overviews.")
    else:
        print("\n⚠️ Some tests failed. The ranking system may need further adjustments.")
    
    return success

if __name__ == "__main__":
    main()
