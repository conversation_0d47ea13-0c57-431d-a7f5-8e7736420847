"""
Test codebase processing and reprocessing functionality.
Tests individual codebase processing, bulk processing, and rebuild operations.
"""
import pytest
import httpx
import asyncio
from typing import Dict, List, Any


@pytest.mark.codebase
@pytest.mark.processing
@pytest.mark.framework
@pytest.mark.slow
class TestCodebaseProcessing:
    """Test codebase processing and reprocessing functionality."""

    @pytest.mark.asyncio
    async def test_list_available_codebases(self, http_client: httpx.AsyncClient, server_health_check):
        """Test listing available codebases."""
        print("\n🔍 Testing codebase discovery...")
        response = await http_client.post("/tools/list_codebases", json={})

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "result" in data
        result_text = data["result"]
        assert isinstance(result_text, str)

        # Should contain codebase information
        assert "Codebases" in result_text or "No codebases found" in result_text

        codebases = self._extract_codebase_names(result_text)
        print(f"📚 Found {len(codebases)} codebases: {codebases}")

        return codebases

    def _extract_codebase_names(self, result_text: str) -> List[str]:
        """Extract codebase names from the list_codebases result."""
        import re

        # Look for actual codebase entries, not headers
        lines = result_text.split('\n')
        codebase_names = []

        for line in lines:
            line = line.strip()
            # Skip headers and empty lines
            if not line or 'Available Codebases' in line or 'Enhanced Multi-Language' in line:
                continue

            # Look for patterns like "**📦 codebase-name**" or "1. **utils**" or "• utils" or "✅ utils"
            patterns = [
                r'^\*\*📦\s+([^*]+)\*\*',     # **📦 codebase-name**
                r'^\d+\.\s*\*\*([^*]+)\*\*',  # 1. **codebase_name**
                r'^•\s*([^\n\|]+)',           # • codebase_name
                r'^✅\s*([^\n\|]+)',          # ✅ codebase_name
                r'^\*\*([^*]+)\*\*\s*\|',     # **codebase_name** | (table format)
            ]

            for pattern in patterns:
                match = re.match(pattern, line)
                if match:
                    name = match.group(1).strip()
                    # Clean up common suffixes and prefixes
                    name = name.replace('(Enhanced)', '').replace('(C/C++)', '').strip()
                    # Skip if it's still a header or empty
                    if name and 'Available' not in name and 'Enhanced' not in name and name not in codebase_names:
                        codebase_names.append(name)
                    break

        return codebase_names

    @pytest.mark.asyncio
    async def test_process_individual_codebase(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processing an individual codebase."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)

        if not codebases:
            pytest.skip("No codebases available for testing")

        # Use the first available codebase
        test_codebase = codebases[0]
        print(f"\n⚙️ Testing individual processing of codebase: {test_codebase}")

        response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "result" in data
        result_text = data["result"]

        # Should indicate successful processing or handle missing source gracefully
        if "Source code directory not found" in result_text:
            print(f"⚠️ Source directory missing for {test_codebase} - this is expected in test environments")
            print(f"📄 Error message: {result_text}")
            # This is acceptable - codebase exists in DB but source directory is missing
            assert test_codebase in result_text or "Source code directory not found" in result_text
        else:
            # Normal successful processing
            assert "Processing Complete" in result_text or "Enhanced" in result_text
            assert test_codebase in result_text
            print(f"✅ Processing completed for {test_codebase}")
            # Show first 200 chars of result for debugging
            print(f"📄 Result preview: {result_text[:200]}...")

    @pytest.mark.asyncio
    async def test_rebuild_individual_codebase(self, http_client: httpx.AsyncClient, server_health_check):
        """Test rebuilding an individual codebase (delete + recreate)."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)
        
        if not codebases:
            pytest.skip("No codebases available for testing")
        
        # Use the first available codebase
        test_codebase = codebases[0]
        
        response = await http_client.post("/tools/rebuild_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })
        
        # Handle different response structures and database permission issues
        if response.status_code == 200:
            data = response.json()

            # Check if it's a successful rebuild
            if "result" in data:
                result_text = data["result"]
                # Should indicate successful rebuild
                assert "Rebuild Complete" in result_text or "Codebase Rebuild Complete" in result_text
                assert test_codebase in result_text
            else:
                # Handle error responses (like database permission issues)
                if "error" in data and "readonly database" in data["error"]:
                    print(f"⚠️ Database permission issue for {test_codebase} - this is expected in some test environments")
                    print(f"📄 Error: {data['error']}")
                    # This is acceptable - database permissions prevent rebuild
                    assert test_codebase in data.get("codebase_name", "")
                else:
                    # Unexpected error format
                    assert False, f"Unexpected response format: {data}"
        else:
            # Should return appropriate error status for permission issues
            assert response.status_code in [400, 403, 500]

    @pytest.mark.asyncio
    async def test_create_new_codebase(self, http_client: httpx.AsyncClient, server_health_check):
        """Test creating a new codebase (should fail if already exists)."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)
        
        if not codebases:
            pytest.skip("No codebases available for testing")
        
        # Try to create an existing codebase (should fail)
        test_codebase = codebases[0]
        
        response = await http_client.post("/tools/create_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })
        
        # Should return error for existing codebase
        if response.status_code == 200:
            data = response.json()
            if "success" in data:
                assert not data["success"]
                assert "already exists" in data.get("error", "").lower()
        else:
            # Some implementations might return 400 for existing codebase
            assert response.status_code in [400, 409]

    @pytest.mark.asyncio
    async def test_bulk_rebuild_all_codebases(self, http_client: httpx.AsyncClient, server_health_check):
        """Test bulk rebuilding all codebases."""
        print("\n🚀 Testing bulk rebuild of all codebases...")
        print("⏱️ This may take up to 5 minutes...")

        # This is a long-running operation, so we use a longer timeout
        timeout = httpx.Timeout(300.0)  # 5 minutes

        response = await http_client.post(
            "/tools/bulk_rebuild_all_codebases",
            json=["build", "test", "bin", "obj", "__pycache__", ".git", ".svn", "node_modules", "dist"],
            timeout=timeout
        )

        assert response.status_code == 200
        data = response.json()

        # Verify response structure - bulk rebuild returns different format
        result_text = data.get("result", data.get("summary", ""))

        # Should indicate bulk processing results
        assert "Bulk Rebuild" in result_text or "BULK REBUILD" in result_text or "Enhanced bulk rebuild" in result_text

        # Should contain statistics
        assert "Total" in result_text or "Statistics" in result_text or "Summary" in result_text

        # Show bulk operation results
        if "total_codebases" in data:
            print(f"📊 Bulk rebuild results: {data['total_codebases']} total, {data.get('successful_count', 0)} successful, {data.get('failed_count', 0)} failed")
        print("✅ Bulk rebuild completed")
        print(f"📄 Summary preview: {result_text[:300]}...")

    @pytest.mark.asyncio
    async def test_processing_with_custom_exclude_dirs(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processing with custom exclude directories."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)
        
        if not codebases:
            pytest.skip("No codebases available for testing")
        
        # Use the first available codebase with custom exclude dirs
        test_codebase = codebases[0]
        custom_exclude_dirs = ["build", "test", "debug", "release", "node_modules", ".git", ".svn"]
        
        response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": custom_exclude_dirs
        })
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "result" in data
        result_text = data["result"]
        
        # Should indicate successful processing or handle missing source gracefully
        if "Source code directory not found" in result_text:
            print(f"⚠️ Source directory missing for {test_codebase} - this is expected in test environments")
        else:
            assert "Processing Complete" in result_text or "Enhanced" in result_text

    @pytest.mark.asyncio
    async def test_processing_nonexistent_codebase(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processing a non-existent codebase."""
        print("\n❌ Testing processing of nonexistent codebase...")

        response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": "nonexistent_codebase_12345",
            "exclude_dirs": ["build", "test"]
        })

        print(f"📡 Response status: {response.status_code}")

        # Should return error for non-existent codebase or handle gracefully
        if response.status_code == 200:
            # Some implementations return 200 with error message
            data = response.json()
            result_text = data.get("result", "")
            print(f"📄 Error message: {result_text[:200]}...")
            # Should contain error indication
            assert ("not found" in result_text.lower() or
                   "error" in result_text.lower() or
                   "failed" in result_text.lower())
            print("✅ Error properly handled with message")
        else:
            # Should return appropriate error status
            assert response.status_code in [400, 404, 500]
            print(f"✅ Error properly handled with status code {response.status_code}")

    @pytest.mark.asyncio
    async def test_processing_performance(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that codebase processing completes within reasonable time."""
        import time

        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)

        if not codebases:
            pytest.skip("No codebases available for testing")

        # Use the first available codebase
        test_codebase = codebases[0]
        print(f"\n⏱️ Testing processing performance for: {test_codebase}")
        print("📏 Maximum allowed time: 5 minutes")

        start_time = time.time()

        response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })

        end_time = time.time()
        processing_time = end_time - start_time

        assert response.status_code == 200

        print(f"⏱️ Processing completed in {processing_time:.2f} seconds")

        # Check if source directory exists
        data = response.json()
        result_text = data.get("result", "")

        if "Source code directory not found" in result_text:
            print(f"⚠️ Source directory missing for {test_codebase} - performance test completed quickly due to missing source")
        else:
            # Processing should complete within reasonable time (5 minutes for individual codebase)
            assert processing_time < 300.0, f"Processing took too long: {processing_time:.2f} seconds"

            if processing_time < 60:
                print("🚀 Excellent performance: < 1 minute")
            elif processing_time < 180:
                print("✅ Good performance: < 3 minutes")
            else:
                print("⚠️ Acceptable performance: < 5 minutes")

    @pytest.mark.asyncio
    async def test_processing_statistics_validation(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that processing returns valid statistics."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)
        
        if not codebases:
            pytest.skip("No codebases available for testing")
        
        # Use the first available codebase
        test_codebase = codebases[0]
        
        response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })
        
        assert response.status_code == 200
        data = response.json()
        
        result_text = data["result"]
        
        # Should contain processing statistics or completion indicators, or handle missing source gracefully
        if "Source code directory not found" in result_text:
            print(f"⚠️ Source directory missing for {test_codebase} - statistics validation skipped")
            # This is acceptable - can't get statistics without source directory
            assert test_codebase in result_text or "Source code directory not found" in result_text
        else:
            statistics_indicators = [
                "files processed", "Files processed", "chunks", "Chunks",
                "languages", "Languages", "processing time", "Processing time",
                "Enhanced", "Complete", "Processing Complete", "files", "Files"
            ]

            has_statistics = any(indicator in result_text for indicator in statistics_indicators)
            assert has_statistics, f"Processing result should contain statistics or completion indicators. Got: {result_text[:200]}..."

    @pytest.mark.asyncio
    async def test_concurrent_processing_safety(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that concurrent processing requests are handled safely."""
        # First get available codebases
        codebases = await self.test_list_available_codebases(http_client, server_health_check)
        
        if len(codebases) < 2:
            pytest.skip("Need at least 2 codebases for concurrent processing test")
        
        # Start two processing operations concurrently
        tasks = []
        for i in range(min(2, len(codebases))):
            task = http_client.post("/tools/process_codebase", json={
                "codebase_name": codebases[i],
                "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
            })
            tasks.append(task)
        
        # Wait for both to complete
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Both should complete successfully or handle concurrency gracefully
        for response in responses:
            if isinstance(response, Exception):
                # Some concurrency exceptions might be acceptable
                continue
            assert response.status_code in [200, 409, 429]  # Success, conflict, or rate limited


@pytest.mark.codebase
@pytest.mark.processing
@pytest.mark.framework
@pytest.mark.slow
class TestBatchCodebaseProcessing:
    """Test batch processing operations for multiple codebases."""

    @pytest.mark.asyncio
    async def test_sequential_processing_multiple_codebases(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processing multiple codebases sequentially."""
        # Ensure server is healthy
        assert server_health_check

        print("\n🔄 Testing sequential processing of multiple codebases...")

        # Get available codebases
        response = await http_client.post("/tools/list_codebases", json={})
        assert response.status_code == 200

        codebases = self._extract_codebase_names(response.json()["result"])

        if len(codebases) < 2:
            pytest.skip("Need at least 2 codebases for batch processing test")

        print(f"📚 Processing first 2 of {len(codebases)} available codebases: {codebases[:2]}")

        # Process first 2 codebases sequentially
        results = []
        for i, codebase in enumerate(codebases[:2], 1):
            print(f"⚙️ Processing {i}/2: {codebase}")

            response = await http_client.post("/tools/process_codebase", json={
                "codebase_name": codebase,
                "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
            })

            assert response.status_code == 200
            data = response.json()
            success = "Processing Complete" in data["result"] or "Enhanced" in data["result"]

            results.append({
                "codebase": codebase,
                "result": data["result"],
                "success": success
            })

            print(f"{'✅' if success else '❌'} {codebase}: {'Success' if success else 'Failed'}")

        # Verify all processed successfully
        successful_count = sum(1 for r in results if r["success"])
        print(f"📊 Sequential processing results: {successful_count}/{len(results)} successful")
        assert successful_count >= 1, "At least one codebase should process successfully"

    def _extract_codebase_names(self, result_text: str) -> List[str]:
        """Extract codebase names from the list_codebases result."""
        import re

        # Look for patterns like "**codebase_name**" or "✅ codebase_name"
        patterns = [
            r'\*\*([^*]+)\*\*',  # **codebase_name**
            r'✅\s+([^\n\|]+)',  # ✅ codebase_name
            r'🚀\s+([^\n\|]+)',  # 🚀 codebase_name
        ]

        codebase_names = []
        for pattern in patterns:
            matches = re.findall(pattern, result_text)
            if matches:
                for match in matches:
                    name = match.strip()
                    # Clean up common suffixes and prefixes
                    name = name.replace('(Enhanced)', '').replace('(C/C++)', '').strip()
                    if name and name not in codebase_names:
                        codebase_names.append(name)

        return codebase_names

    @pytest.mark.asyncio
    async def test_bulk_rebuild_statistics(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that bulk rebuild returns comprehensive statistics."""
        # Ensure server is healthy
        assert server_health_check

        # This is a long-running operation
        timeout = httpx.Timeout(300.0)  # 5 minutes

        response = await http_client.post(
            "/tools/bulk_rebuild_all_codebases",
            json=["build", "test", "bin", "obj", "__pycache__", ".git", ".svn", "node_modules", "dist"],
            timeout=timeout
        )

        assert response.status_code == 200
        data = response.json()

        result_text = data.get("result", data.get("summary", ""))

        # Should contain comprehensive statistics
        expected_stats = [
            "Total", "Successful", "Failed", "Processing time",
            "codebases", "Statistics", "Summary", "BULK REBUILD"
        ]

        stats_found = sum(1 for stat in expected_stats if stat in result_text)
        assert stats_found >= 2, f"Should contain at least 2 statistical indicators, found {stats_found}"

    @pytest.mark.asyncio
    async def test_processing_with_different_exclude_patterns(self, http_client: httpx.AsyncClient, server_health_check):
        """Test processing with different exclude directory patterns."""
        # Ensure server is healthy
        assert server_health_check

        print("\n📁 Testing different exclude directory patterns...")

        # Get available codebases
        response = await http_client.post("/tools/list_codebases", json={})
        assert response.status_code == 200

        codebases = self._extract_codebase_names(response.json()["result"])

        if not codebases:
            pytest.skip("No codebases available for testing")

        test_codebase = codebases[0]
        print(f"🎯 Testing exclude patterns with codebase: {test_codebase}")

        # Test different exclude patterns
        exclude_patterns = [
            (["build", "test"], "Minimal"),
            (["build", "test", "bin", "obj", "__pycache__", ".git"], "Standard"),
            (["build", "test", "debug", "release", "node_modules", ".git", ".svn", "target"], "Comprehensive"),
            ([], "No exclusions")
        ]

        for i, (exclude_dirs, pattern_name) in enumerate(exclude_patterns):
            print(f"🔧 Testing pattern {i+1}/4: {pattern_name} ({len(exclude_dirs)} directories)")

            response = await http_client.post("/tools/process_codebase", json={
                "codebase_name": test_codebase,
                "exclude_dirs": exclude_dirs
            })

            assert response.status_code == 200, f"Pattern {pattern_name} failed: {exclude_dirs}"
            data = response.json()

            # Should indicate successful processing
            success = "Processing Complete" in data["result"] or "Enhanced" in data["result"]
            assert success
            print(f"✅ {pattern_name} pattern: Success")

        print("🎉 All exclude patterns tested successfully")

    @pytest.mark.asyncio
    async def test_processing_error_handling(self, http_client: httpx.AsyncClient, server_health_check):
        """Test error handling in processing operations."""
        # Ensure server is healthy
        assert server_health_check

        print("\n🚨 Testing error handling for invalid requests...")

        # Test various error conditions
        error_test_cases = [
            {
                "name": "empty_codebase_name",
                "payload": {"codebase_name": "", "exclude_dirs": ["build"]},
                "expected_status": [400, 422]
            },
            {
                "name": "invalid_exclude_dirs",
                "payload": {"codebase_name": "test", "exclude_dirs": "not_a_list"},
                "expected_status": [400, 422]
            },
            {
                "name": "missing_codebase_name",
                "payload": {"exclude_dirs": ["build"]},
                "expected_status": [400, 422]
            }
        ]

        for test_case in error_test_cases:
            print(f"🔍 Testing error case: {test_case['name']}")

            response = await http_client.post("/tools/process_codebase", json=test_case["payload"])

            print(f"📡 Response status: {response.status_code}")

            # Some implementations might return 200 with error message instead of error status
            if response.status_code == 200:
                data = response.json()
                result_text = data.get("result", "")
                print(f"📄 Error message: {result_text[:100]}...")
                # Should contain error indication
                assert ("error" in result_text.lower() or
                       "failed" in result_text.lower() or
                       "not found" in result_text.lower()), \
                    f"Test case '{test_case['name']}' should return error message"
                print("✅ Error properly handled with message")
            else:
                assert response.status_code in test_case["expected_status"], \
                    f"Test case '{test_case['name']}' should return status in {test_case['expected_status']}"
                print(f"✅ Error properly handled with status {response.status_code}")

        print("🎉 All error handling tests passed")

    @pytest.mark.asyncio
    async def test_rebuild_vs_process_comparison(self, http_client: httpx.AsyncClient, server_health_check):
        """Test the difference between rebuild and process operations."""
        # Ensure server is healthy
        assert server_health_check

        # Get available codebases
        response = await http_client.post("/tools/list_codebases", json={})
        assert response.status_code == 200

        codebases = self._extract_codebase_names(response.json()["result"])

        if not codebases:
            pytest.skip("No codebases available for testing")

        test_codebase = codebases[0]

        # First do a regular process
        process_response = await http_client.post("/tools/process_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })

        assert process_response.status_code == 200
        process_result = process_response.json()["result"]

        # Then do a rebuild
        rebuild_response = await http_client.post("/tools/rebuild_codebase", json={
            "codebase_name": test_codebase,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        })

        # Handle different response structures
        if rebuild_response.status_code == 200:
            rebuild_data = rebuild_response.json()
            rebuild_result = rebuild_data.get("result", rebuild_data.get("summary", rebuild_data.get("error", "")))

            # Both should succeed but with different messages
            assert "Processing Complete" in process_result or "Enhanced" in process_result

            # Rebuild might fail due to database issues, but should handle gracefully
            if "error" not in rebuild_result.lower() and "failed" not in rebuild_result.lower():
                assert "Rebuild Complete" in rebuild_result or "Codebase Rebuild Complete" in rebuild_result
                # Rebuild should indicate it's a rebuild operation
                assert "Rebuild" in rebuild_result
        else:
            # Rebuild might fail due to permissions or database issues
            assert rebuild_response.status_code in [400, 403, 500]
