#!/bin/bash
# Remote file verification script for /home/<USER>/home-ai-system/code_analyzer_server

echo "🔍 Checking Remote Files on home-ai-server"
echo "=================================================="

cd /home/<USER>/home-ai-system/code_analyzer_server || exit 1

echo "📁 Current directory: $(pwd)"
echo ""

# Check essential files
echo "🔧 Essential Application Files:"

if [ -e "main.py" ]; then
    echo "✅ main.py - Main application entry point"
else
    echo "❌ main.py - Main application entry point (MISSING)"
fi
if [ -e "framework_integration.py" ]; then
    echo "✅ framework_integration.py - Framework integration system"
else
    echo "❌ framework_integration.py - Framework integration system (MISSING)"
fi
if [ -e "language_framework.py" ]; then
    echo "✅ language_framework.py - Language processing framework"
else
    echo "❌ language_framework.py - Language processing framework (MISSING)"
fi
if [ -e "language_processors.py" ]; then
    echo "✅ language_processors.py - Language-specific processors"
else
    echo "❌ language_processors.py - Language-specific processors (MISSING)"
fi
if [ -e "language_registry.py" ]; then
    echo "✅ language_registry.py - Language registry system"
else
    echo "❌ language_registry.py - Language registry system (MISSING)"
fi
if [ -e "gpu_infrastructure.py" ]; then
    echo "✅ gpu_infrastructure.py - GPU infrastructure management"
else
    echo "❌ gpu_infrastructure.py - GPU infrastructure management (MISSING)"
fi
if [ -e "processing_pipeline.py" ]; then
    echo "✅ processing_pipeline.py - Processing pipeline system"
else
    echo "❌ processing_pipeline.py - Processing pipeline system (MISSING)"
fi
if [ -e "chunk_system.py" ]; then
    echo "✅ chunk_system.py - Code chunking system"
else
    echo "❌ chunk_system.py - Code chunking system (MISSING)"
fi
if [ -e "vector_db_creator.py" ]; then
    echo "✅ vector_db_creator.py - Vector database creation"
else
    echo "❌ vector_db_creator.py - Vector database creation (MISSING)"
fi
if [ -e "open_webui_code_analyzer_tool.py" ]; then
    echo "✅ open_webui_code_analyzer_tool.py - OpenWebUI integration tool"
else
    echo "❌ open_webui_code_analyzer_tool.py - OpenWebUI integration tool (MISSING)"
fi
if [ -e "intent_detection_service.py" ]; then
    echo "✅ intent_detection_service.py - Intent detection service"
else
    echo "❌ intent_detection_service.py - Intent detection service (MISSING)"
fi
if [ -e "semantic_patterns.py" ]; then
    echo "✅ semantic_patterns.py - Semantic pattern matching"
else
    echo "❌ semantic_patterns.py - Semantic pattern matching (MISSING)"
fi
if [ -e "metta_processor.py" ]; then
    echo "✅ metta_processor.py - Metta language processor"
else
    echo "❌ metta_processor.py - Metta language processor (MISSING)"
fi
if [ -e "tree_sitter_chunker.py" ]; then
    echo "✅ tree_sitter_chunker.py - Tree-sitter based chunking"
else
    echo "❌ tree_sitter_chunker.py - Tree-sitter based chunking (MISSING)"
fi
if [ -e "embedding_config.py" ]; then
    echo "✅ embedding_config.py - Embedding configuration"
else
    echo "❌ embedding_config.py - Embedding configuration (MISSING)"
fi
if [ -e "code_preprocessor.py" ]; then
    echo "✅ code_preprocessor.py - Code preprocessing system"
else
    echo "❌ code_preprocessor.py - Code preprocessing system (MISSING)"
fi
if [ -e "requirements.txt" ]; then
    echo "✅ requirements.txt - Python dependencies"
else
    echo "❌ requirements.txt - Python dependencies (MISSING)"
fi
if [ -e "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml - Docker composition configuration"
else
    echo "❌ docker-compose.yml - Docker composition configuration (MISSING)"
fi
if [ -e "Dockerfile" ]; then
    echo "✅ Dockerfile - Docker container configuration"
else
    echo "❌ Dockerfile - Docker container configuration (MISSING)"
fi
if [ -e "pytest.ini" ]; then
    echo "✅ pytest.ini - Testing configuration"
else
    echo "❌ pytest.ini - Testing configuration (MISSING)"
fi
if [ -e "config/intent_detection.json5" ]; then
    echo "✅ config/intent_detection.json5 - Intent detection configuration"
else
    echo "❌ config/intent_detection.json5 - Intent detection configuration (MISSING)"
fi
if [ -e "unit-tests/conftest.py" ]; then
    echo "✅ unit-tests/conftest.py - Test configuration"
else
    echo "❌ unit-tests/conftest.py - Test configuration (MISSING)"
fi
if [ -e "unit-tests/test_analysis_endpoints.py" ]; then
    echo "✅ unit-tests/test_analysis_endpoints.py - Analysis endpoint tests"
else
    echo "❌ unit-tests/test_analysis_endpoints.py - Analysis endpoint tests (MISSING)"
fi
if [ -e "unit-tests/test_basic_connectivity.py" ]; then
    echo "✅ unit-tests/test_basic_connectivity.py - Basic connectivity tests"
else
    echo "❌ unit-tests/test_basic_connectivity.py - Basic connectivity tests (MISSING)"
fi
if [ -e "unit-tests/test_codebase_management.py" ]; then
    echo "✅ unit-tests/test_codebase_management.py - Codebase management tests"
else
    echo "❌ unit-tests/test_codebase_management.py - Codebase management tests (MISSING)"
fi
if [ -e "unit-tests/test_configuration.py" ]; then
    echo "✅ unit-tests/test_configuration.py - Configuration tests"
else
    echo "❌ unit-tests/test_configuration.py - Configuration tests (MISSING)"
fi
if [ -e "unit-tests/test_data_quality.py" ]; then
    echo "✅ unit-tests/test_data_quality.py - Data quality tests"
else
    echo "❌ unit-tests/test_data_quality.py - Data quality tests (MISSING)"
fi
if [ -e "unit-tests/test_deployment_features.py" ]; then
    echo "✅ unit-tests/test_deployment_features.py - Deployment feature tests"
else
    echo "❌ unit-tests/test_deployment_features.py - Deployment feature tests (MISSING)"
fi
if [ -e "unit-tests/test_framework_integration.py" ]; then
    echo "✅ unit-tests/test_framework_integration.py - Framework integration tests"
else
    echo "❌ unit-tests/test_framework_integration.py - Framework integration tests (MISSING)"
fi
if [ -e "unit-tests/test_intent_detection.py" ]; then
    echo "✅ unit-tests/test_intent_detection.py - Intent detection tests"
else
    echo "❌ unit-tests/test_intent_detection.py - Intent detection tests (MISSING)"
fi
if [ -e "unit-tests/test_language_support.py" ]; then
    echo "✅ unit-tests/test_language_support.py - Language support tests"
else
    echo "❌ unit-tests/test_language_support.py - Language support tests (MISSING)"
fi
if [ -e "unit-tests/test_openwebui_integration.py" ]; then
    echo "✅ unit-tests/test_openwebui_integration.py - OpenWebUI integration tests"
else
    echo "❌ unit-tests/test_openwebui_integration.py - OpenWebUI integration tests (MISSING)"
fi
if [ -e "unit-tests/test_performance.py" ]; then
    echo "✅ unit-tests/test_performance.py - Performance tests"
else
    echo "❌ unit-tests/test_performance.py - Performance tests (MISSING)"
fi
if [ -e "unit-tests/test_search_functionality.py" ]; then
    echo "✅ unit-tests/test_search_functionality.py - Search functionality tests"
else
    echo "❌ unit-tests/test_search_functionality.py - Search functionality tests (MISSING)"
fi
if [ -e "unit-tests/test_server_health.py" ]; then
    echo "✅ unit-tests/test_server_health.py - Server health tests"
else
    echo "❌ unit-tests/test_server_health.py - Server health tests (MISSING)"
fi
if [ -e "unit-tests/test_simple_verification.py" ]; then
    echo "✅ unit-tests/test_simple_verification.py - Simple verification tests"
else
    echo "❌ unit-tests/test_simple_verification.py - Simple verification tests (MISSING)"
fi
if [ -e "run_unit_tests.bat" ]; then
    echo "✅ run_unit_tests.bat - Windows test runner"
else
    echo "❌ run_unit_tests.bat - Windows test runner (MISSING)"
fi
if [ -e "run_unit_tests.sh" ]; then
    echo "✅ run_unit_tests.sh - Linux test runner"
else
    echo "❌ run_unit_tests.sh - Linux test runner (MISSING)"
fi

echo ""
echo "📊 Directory Structure:"
find . -maxdepth 2 -type f -name "*.py" | head -20
echo ""

echo "📦 Docker Files:"
ls -la docker-compose.yml Dockerfile 2>/dev/null || echo "Docker files not found"
echo ""

echo "🧪 Unit Tests:"
ls -la unit-tests/ 2>/dev/null || echo "Unit tests directory not found"
echo ""

echo "⚙️ Configuration:"
ls -la config/ requirements.txt pytest.ini 2>/dev/null || echo "Configuration files not found"
echo ""

echo "🗂️ Source Code Repositories:"
ls -la source_code/ 2>/dev/null || echo "Source code directory not found"
