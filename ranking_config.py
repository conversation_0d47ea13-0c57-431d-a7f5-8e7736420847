"""
Configuration for enhanced search ranking
Provides tunable parameters for different search scenarios
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class SearchScenario(Enum):
    """Universal search scenarios that apply to all programming languages"""
    FUNCTION_IMPLEMENTATION = "function_implementation"  # Finding specific code implementations
    RESOURCE_MANAGEMENT = "resource_management"         # Memory, files, connections, etc. (where applicable)
    ARCHITECTURAL_OVERVIEW = "architectural_overview"   # High-level design and structure
    DEBUGGING = "debugging"                            # Problem-solving and troubleshooting
    API_DOCUMENTATION = "api_documentation"            # Usage examples and documentation

@dataclass
class RankingProfile:
    """Ranking profile for different search scenarios"""
    name: str
    description: str
    ranking_weights: Dict[str, float]
    ranking_penalties: Dict[str, float]
    function_keywords: Dict[str, List[str]]
    prefer_implementations: bool = True

# Predefined ranking profiles
RANKING_PROFILES = {
    SearchScenario.FUNCTION_IMPLEMENTATION: RankingProfile(
        name="Function Implementation",
        description="Optimized for finding specific function/method implementations across all languages",
        ranking_weights={
            'exact_function_match': 15.0,      # Highest priority for exact matches
            'function_name_in_content': 8.0,   # High priority for function names
            'related_function': 4.0,
            'implementation_code': 5.0,        # Boost actual code implementations
            'has_function_signature': 3.0,
            'specific_language': 2.0,
            'has_documentation': 1.2,          # Lower priority for docs in implementation search
            'code_quality': 1.5,
            'architectural_overview': 0.3,     # Very low priority for overviews
            'system_metadata': 0.1             # Minimal priority for metadata
        },
        ranking_penalties={
            'multi_language_chunk': -3.0,      # Strong penalty for multi-language chunks
            'architectural_pattern': -2.5,     # Strong penalty for architectural patterns
            'too_many_functions': -2.0,        # Penalty for function lists
            'generic_metadata': -4.0           # Strong penalty for generic metadata
        },
        function_keywords={},  # No specific keywords - use generic detection
        prefer_implementations=True
    ),
    
    SearchScenario.RESOURCE_MANAGEMENT: RankingProfile(
        name="Resource Management",
        description="Optimized for resource management (memory, files, connections, etc.) across all languages",
        ranking_weights={
            'exact_function_match': 12.0,
            'function_name_in_content': 6.0,
            'related_function': 5.0,
            'implementation_code': 4.0,
            'has_function_signature': 3.5,
            'specific_language': 2.5,          # Language-specific resource management
            'has_documentation': 1.8,          # Documentation helps with resource management
            'code_quality': 1.4,
            'architectural_overview': 0.4,
            'system_metadata': 0.2
        },
        ranking_penalties={
            'multi_language_chunk': -2.5,
            'architectural_pattern': -2.0,
            'too_many_functions': -1.5,
            'generic_metadata': -3.0
        },
        function_keywords={},  # No hardcoded keywords - use generic detection
        prefer_implementations=True
    ),
    
    SearchScenario.ARCHITECTURAL_OVERVIEW: RankingProfile(
        name="Architectural Overview",
        description="Optimized for architectural and design pattern searches",
        ranking_weights={
            'exact_function_match': 3.0,       # Lower priority for specific functions
            'function_name_in_content': 2.0,
            'related_function': 2.5,
            'implementation_code': 1.5,        # Lower priority for implementations
            'has_function_signature': 1.8,
            'specific_language': 1.2,
            'has_documentation': 3.0,          # Higher priority for documentation
            'code_quality': 2.0,
            'architectural_overview': 5.0,     # High priority for architectural content
            'system_metadata': 3.0             # Higher priority for system metadata
        },
        ranking_penalties={
            'multi_language_chunk': 0.0,       # No penalty for multi-language in architectural search
            'architectural_pattern': 0.0,      # No penalty for architectural patterns
            'too_many_functions': -0.5,        # Minimal penalty
            'generic_metadata': -1.0           # Lower penalty for metadata
        },
        function_keywords={},
        prefer_implementations=False
    ),
    
    SearchScenario.DEBUGGING: RankingProfile(
        name="Debugging",
        description="Optimized for debugging and error investigation",
        ranking_weights={
            'exact_function_match': 10.0,
            'function_name_in_content': 7.0,
            'related_function': 4.0,
            'implementation_code': 6.0,        # High priority for actual code
            'has_function_signature': 3.0,
            'specific_language': 2.0,
            'has_documentation': 2.5,          # Documentation can help with debugging
            'code_quality': 1.0,               # Less important for debugging
            'architectural_overview': 1.0,
            'system_metadata': 0.5
        },
        ranking_penalties={
            'multi_language_chunk': -1.5,
            'architectural_pattern': -1.0,
            'too_many_functions': -1.0,
            'generic_metadata': -2.0
        },
        function_keywords={
            'error_handling': ['error', 'exception', 'fail', 'abort', 'assert', 'debug'],
            'logging': ['log', 'print', 'trace', 'debug', 'info', 'warn', 'error']
        },
        prefer_implementations=True
    ),
    
    SearchScenario.API_DOCUMENTATION: RankingProfile(
        name="API Documentation",
        description="Optimized for API documentation and usage examples",
        ranking_weights={
            'exact_function_match': 8.0,
            'function_name_in_content': 5.0,
            'related_function': 3.0,
            'implementation_code': 2.0,        # Lower priority for implementation details
            'has_function_signature': 4.0,     # High priority for signatures
            'specific_language': 1.5,
            'has_documentation': 6.0,          # Highest priority for documentation
            'code_quality': 2.5,
            'architectural_overview': 2.0,
            'system_metadata': 1.5
        },
        ranking_penalties={
            'multi_language_chunk': -0.5,      # Minimal penalty for multi-language
            'architectural_pattern': 0.0,      # No penalty for patterns
            'too_many_functions': 0.0,         # No penalty for function lists in docs
            'generic_metadata': -1.0
        },
        function_keywords={},
        prefer_implementations=False
    )
}

class RankingConfigManager:
    """Manages ranking configuration and profiles"""
    
    def __init__(self):
        self.current_profile = RANKING_PROFILES[SearchScenario.FUNCTION_IMPLEMENTATION]
        self.custom_profiles = {}
    
    def get_profile(self, scenario: SearchScenario) -> RankingProfile:
        """Get ranking profile for scenario"""
        return RANKING_PROFILES.get(scenario, self.current_profile)
    
    def set_profile(self, scenario: SearchScenario):
        """Set current ranking profile"""
        if scenario in RANKING_PROFILES:
            self.current_profile = RANKING_PROFILES[scenario]
        else:
            raise ValueError(f"Unknown scenario: {scenario}")
    
    def create_custom_profile(self, name: str, base_scenario: SearchScenario,
                            weight_overrides: Optional[Dict[str, float]] = None,
                            penalty_overrides: Optional[Dict[str, float]] = None) -> RankingProfile:
        """Create custom ranking profile based on existing scenario"""
        base_profile = RANKING_PROFILES[base_scenario]
        
        # Create new weights and penalties
        new_weights = base_profile.ranking_weights.copy()
        new_penalties = base_profile.ranking_penalties.copy()
        
        if weight_overrides:
            new_weights.update(weight_overrides)
        
        if penalty_overrides:
            new_penalties.update(penalty_overrides)
        
        custom_profile = RankingProfile(
            name=name,
            description=f"Custom profile based on {base_profile.name}",
            ranking_weights=new_weights,
            ranking_penalties=new_penalties,
            function_keywords=base_profile.function_keywords.copy(),
            prefer_implementations=base_profile.prefer_implementations
        )
        
        self.custom_profiles[name] = custom_profile
        return custom_profile
    
    def get_profile_for_query(self, query: str) -> RankingProfile:
        """Automatically select best profile for query - generic approach"""
        query_lower = query.lower()

        # Function/method implementation queries (most common)
        implementation_keywords = ['function', 'method', 'class', 'implement', 'code', 'def', 'fn', 'func', 'procedure']
        if any(keyword in query_lower for keyword in implementation_keywords):
            return RANKING_PROFILES[SearchScenario.FUNCTION_IMPLEMENTATION]

        # Debugging/troubleshooting queries
        debug_keywords = ['debug', 'error', 'fail', 'crash', 'bug', 'issue', 'problem', 'fix', 'troubleshoot']
        if any(keyword in query_lower for keyword in debug_keywords):
            return RANKING_PROFILES[SearchScenario.DEBUGGING]

        # Documentation/usage queries
        doc_keywords = ['api', 'documentation', 'usage', 'example', 'how to', 'tutorial', 'guide', 'help']
        if any(keyword in query_lower for keyword in doc_keywords):
            return RANKING_PROFILES[SearchScenario.API_DOCUMENTATION]

        # Architectural/design queries
        arch_keywords = ['architecture', 'design', 'pattern', 'structure', 'overview', 'organization', 'layout']
        if any(keyword in query_lower for keyword in arch_keywords):
            return RANKING_PROFILES[SearchScenario.ARCHITECTURAL_OVERVIEW]

        # Check if query contains specific identifiers (likely looking for implementations)
        import re
        if re.search(r'\b[a-zA-Z_][a-zA-Z0-9_]*\s*\(', query) or re.search(r'\b[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*', query):
            return RANKING_PROFILES[SearchScenario.FUNCTION_IMPLEMENTATION]

        # Default to function implementation for specific queries
        return RANKING_PROFILES[SearchScenario.FUNCTION_IMPLEMENTATION]
    
    def export_config(self) -> Dict[str, Any]:
        """Export current configuration"""
        return {
            'current_profile': {
                'name': self.current_profile.name,
                'ranking_weights': self.current_profile.ranking_weights,
                'ranking_penalties': self.current_profile.ranking_penalties,
                'function_keywords': self.current_profile.function_keywords,
                'prefer_implementations': self.current_profile.prefer_implementations
            },
            'available_scenarios': [scenario.value for scenario in SearchScenario],
            'custom_profiles': {name: {
                'name': profile.name,
                'description': profile.description,
                'ranking_weights': profile.ranking_weights,
                'ranking_penalties': profile.ranking_penalties
            } for name, profile in self.custom_profiles.items()}
        }

# Global configuration manager instance
ranking_config_manager = RankingConfigManager()
