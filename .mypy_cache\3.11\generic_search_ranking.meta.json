{"data_mtime": 1753289331, "dep_lines": [6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["re", "json", "typing", "dataclasses", "enum", "logging", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "2b010f7f1ba9a1bbd2fb75f4d28143d53633f38c", "id": "generic_search_ranking", "ignore_all": false, "interface_hash": "b83363dcde3e5a06c489c29a76ec2ccad1873254", "mtime": 1753290054, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\generic_search_ranking.py", "plugin_data": null, "size": 32894, "suppressed": [], "version_id": "1.15.0"}