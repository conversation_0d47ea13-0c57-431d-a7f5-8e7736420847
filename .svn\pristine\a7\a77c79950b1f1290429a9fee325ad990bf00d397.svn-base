"""
Simple verification tests that don't require the server to be running.
These tests verify that the testing framework is working correctly.
"""
import pytest
import asyncio


class TestSimpleVerification:
    """Simple tests to verify the testing framework is working."""

    def test_basic_math(self):
        """Test basic math operations."""
        assert 2 + 2 == 4
        assert 10 - 5 == 5
        assert 3 * 4 == 12

    def test_string_operations(self):
        """Test string operations."""
        test_string = "Hello, World!"
        assert len(test_string) == 13
        assert test_string.lower() == "hello, world!"
        assert "World" in test_string

    def test_list_operations(self):
        """Test list operations."""
        test_list = [1, 2, 3, 4, 5]
        assert len(test_list) == 5
        assert test_list[0] == 1
        assert test_list[-1] == 5
        assert sum(test_list) == 15

    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """Test async functionality."""
        async def async_add(a, b):
            await asyncio.sleep(0.01)  # Simulate async work
            return a + b
        
        result = await async_add(5, 3)
        assert result == 8

    def test_pytest_markers(self):
        """Test that pytest markers work."""
        # This test should always pass
        assert True

    @pytest.mark.parametrize("input,expected", [
        (1, 2),
        (2, 4),
        (3, 6),
        (4, 8),
    ])
    def test_parametrized_test(self, input, expected):
        """Test parametrized testing."""
        assert input * 2 == expected

    def test_exception_handling(self):
        """Test exception handling."""
        with pytest.raises(ZeroDivisionError):
            result = 10 / 0

    def test_configuration_values(self):
        """Test that we can access configuration values."""
        # Test that basic Python functionality works
        import os
        import sys
        
        assert sys.version_info.major >= 3
        assert os.path.exists(".")  # Current directory should exist
