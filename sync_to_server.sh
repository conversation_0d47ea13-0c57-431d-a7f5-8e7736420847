#!/bin/bash
# Sync all essential files to home-ai-server
# Run this script from the local repository root

REMOTE_HOST="home-ai-server"
REMOTE_USER="fvaneijk"
REMOTE_PATH="/home/<USER>/home-ai-system/code_analyzer_server"
LOCAL_PATH="."

echo "🚀 Syncing OpenWebUI RAG Code Server to $REMOTE_HOST"
echo "============================================================"
echo "Local:  $LOCAL_PATH"
echo "Remote: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH"
echo ""

# Ensure remote directory exists
echo "📁 Ensuring remote directory exists..."
ssh $REMOTE_USER@$REMOTE_HOST "mkdir -p $REMOTE_PATH"

# Sync core application files
echo "📤 Syncing core application files..."
rsync -av --progress \
    main.py \
    framework_integration.py \
    language_framework.py \
    language_processors.py \
    language_registry.py \
    gpu_infrastructure.py \
    processing_pipeline.py \
    chunk_system.py \
    vector_db_creator.py \
    open_webui_code_analyzer_tool.py \
    intent_detection_service.py \
    semantic_patterns.py \
    metta_processor.py \
    tree_sitter_chunker.py \
    embedding_config.py \
    code_preprocessor.py \
    $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# Sync configuration files
echo "⚙️ Syncing configuration files..."
rsync -av --progress \
    requirements.txt \
    docker-compose.yml \
    Dockerfile \
    pytest.ini \
    $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# Sync configuration directory
echo "📋 Syncing config directory..."
rsync -av --progress config/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/config/

# Sync unit test framework
echo "🧪 Syncing unit test framework..."
rsync -av --progress unit-tests/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/unit-tests/

# Sync execution scripts
echo "🔧 Syncing execution scripts..."
rsync -av --progress \
    run_unit_tests.bat \
    run_unit_tests.sh \
    $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# Sync templates if they exist
if [ -d "templates" ]; then
    echo "🎨 Syncing templates..."
    rsync -av --progress templates/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/templates/
fi

# Sync documentation if it exists
if [ -d "docs" ]; then
    echo "📚 Syncing documentation..."
    rsync -av --progress docs/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/docs/
fi

# Copy the remote check script
echo "📝 Copying remote verification script..."
scp check_remote_files.sh $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

# Make scripts executable
echo "🔐 Making scripts executable..."
ssh $REMOTE_USER@$REMOTE_HOST "chmod +x $REMOTE_PATH/run_unit_tests.sh $REMOTE_PATH/check_remote_files.sh"

echo ""
echo "✅ Sync completed!"
echo ""
echo "🔍 Next steps:"
echo "1. SSH to the server: ssh $REMOTE_USER@$REMOTE_HOST"
echo "2. Navigate to: cd $REMOTE_PATH"
echo "3. Verify files: bash check_remote_files.sh"
echo "4. Test the system: bash run_unit_tests.sh"
echo "5. Start the server: docker-compose up -d"
echo ""
echo "📊 Files synced:"
echo "   - Core application files (16 files)"
echo "   - Configuration files (4 files)"
echo "   - Unit test framework (15 test modules)"
echo "   - Execution scripts (2 files)"
echo "   - Verification script (1 file)"
echo ""
echo "🎯 Your OpenWebUI RAG Code Server is ready for deployment!"
