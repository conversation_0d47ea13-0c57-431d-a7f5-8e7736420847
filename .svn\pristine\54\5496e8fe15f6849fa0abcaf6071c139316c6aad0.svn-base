"""
Test analysis service endpoints.
"""
import pytest
import httpx


class TestAnalysisEndpoints:
    """Test analysis service functionality."""

    @pytest.mark.asyncio
    async def test_analysis_health(self, http_client: httpx.AsyncClient, server_health_check):
        """Test the analysis health endpoint."""
        response = await http_client.get("/analysis/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "service" in data
        assert "features" in data
        assert data["status"] == "healthy"

    @pytest.mark.asyncio
    async def test_analysis_status(self, http_client: httpx.AsyncClient):
        """Test the analysis status endpoint."""
        response = await http_client.get("/analysis/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "analyzed_codebases" in data or "error" in data

    @pytest.mark.asyncio
    async def test_analyze_codebase_tool_endpoint(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test the analyze codebase tool endpoint."""
        payload = {"codebase_name": sample_codebase_name}
        response = await http_client.post("/tools/analyze_codebase", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data

    @pytest.mark.asyncio
    async def test_enhanced_ask_endpoint(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test the enhanced ask endpoint."""
        payload = {
            "question": "What are the main components?",
            "codebase_name": sample_codebase_name
        }
        response = await http_client.post("/enhanced_ask", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "answer" in data or "error" in data

    @pytest.mark.asyncio
    async def test_enhanced_context_endpoint(self, http_client: httpx.AsyncClient, sample_codebase_name, sample_query):
        """Test the enhanced context endpoint."""
        payload = {
            "query": sample_query,
            "codebase_name": sample_codebase_name
        }
        response = await http_client.post("/enhanced_context", json=payload)
        assert response.status_code == 200

        data = response.json()
        # The actual response contains context_chunks, query, codebase, etc.
        assert "context_chunks" in data or "error" in data
        if "context_chunks" in data:
            assert "query" in data
            assert "codebase" in data
