{"python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["unit-tests", "-v", "--tb=short"], "python.testing.pytestPath": "python -m pytest", "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.testing.cwd": "${workspaceFolder}", "python.defaultInterpreterPath": "python", "files.associations": {"*.py": "python"}, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.formatting.provider": "none", "editor.formatOnSave": false, "python.testing.debugPort": 3000, "testExplorer.useNativeTesting": true, "python.testing.promptToConfigure": false}