#!/usr/bin/env python3
"""
Unit tests for semantic integration and chunking functionality
Tests both local semantic analysis and deployed server integration
"""

import pytest
import sys
import os
import json
import time
import requests
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_chunking_enhancement import SemanticChunker, integrate_semantic_chunking, EnhancedQueryProcessor
from tree_sitter_chunker import TreeSitterChunker
from framework_integration import IntegratedCodeAnalysisSystem

class TestSemanticIntegration:
    """Test suite for semantic integration functionality"""
    
    @pytest.fixture
    def sample_c_code(self):
        """Sample C code for testing"""
        return '''
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024
#define MAX_ITEMS 50

typedef struct memory_block {
    void* ptr;
    size_t size;
    int is_allocated;
} memory_block_t;

static memory_block_t blocks[MAX_ITEMS];
static int block_count = 0;

/**
 * Initialize memory management system
 * @return 0 on success, -1 on failure
 */
int memory_init(void) {
    memset(blocks, 0, sizeof(blocks));
    block_count = 0;
    return 0;
}

/**
 * Allocate memory block
 * @param size Size in bytes
 * @return Pointer to allocated memory or NULL
 */
void* memory_alloc(size_t size) {
    if (size == 0 || block_count >= MAX_ITEMS) {
        return NULL;
    }
    
    void* ptr = malloc(size);
    if (!ptr) {
        return NULL;
    }
    
    blocks[block_count].ptr = ptr;
    blocks[block_count].size = size;
    blocks[block_count].is_allocated = 1;
    block_count++;
    
    return ptr;
}

/**
 * Free allocated memory
 * @param ptr Pointer to free
 */
void memory_free(void* ptr) {
    if (!ptr) return;
    
    for (int i = 0; i < block_count; i++) {
        if (blocks[i].ptr == ptr && blocks[i].is_allocated) {
            free(ptr);
            blocks[i].ptr = NULL;
            blocks[i].size = 0;
            blocks[i].is_allocated = 0;
            break;
        }
    }
}

/**
 * Get memory statistics
 * @return Number of allocated blocks
 */
int memory_get_allocated_count(void) {
    int count = 0;
    for (int i = 0; i < block_count; i++) {
        if (blocks[i].is_allocated) {
            count++;
        }
    }
    return count;
}
'''

    @pytest.fixture
    def semantic_chunker(self):
        """Create semantic chunker instance"""
        return SemanticChunker()

    @pytest.fixture
    def enhanced_chunker(self):
        """Create enhanced chunker with semantic integration"""
        tree_chunker = TreeSitterChunker()
        return integrate_semantic_chunking(tree_chunker)

    def test_semantic_chunker_initialization(self, semantic_chunker):
        """Test SemanticChunker initialization"""
        assert hasattr(semantic_chunker, 'relationships')
        assert hasattr(semantic_chunker, 'code_elements')
        assert hasattr(semantic_chunker, 'dependency_graph')
        
        assert isinstance(semantic_chunker.relationships, list)
        assert isinstance(semantic_chunker.code_elements, dict)
        assert isinstance(semantic_chunker.dependency_graph, dict)

    def test_semantic_analysis_basic(self, semantic_chunker, sample_c_code):
        """Test basic semantic analysis functionality"""
        file_contents = {"test_memory.c": sample_c_code}
        
        analysis = semantic_chunker.analyze_codebase_semantics(file_contents)
        
        # Verify return structure
        assert isinstance(analysis, dict)
        required_keys = ['elements', 'relationships', 'clusters', 'enhanced_chunks']
        for key in required_keys:
            assert key in analysis, f"Missing key: {key}"
        
        # Verify data types
        assert isinstance(analysis['elements'], dict)
        assert isinstance(analysis['relationships'], list)
        assert isinstance(analysis['clusters'], list)
        assert isinstance(analysis['enhanced_chunks'], list)
        
        # Should find some elements in the C code
        assert len(analysis['elements']) > 0, "Should find code elements"

    def test_semantic_integration_with_chunker(self, enhanced_chunker, sample_c_code):
        """Test semantic integration with existing chunker"""
        chunks = enhanced_chunker("test_memory.c", sample_c_code, "c_cpp")
        
        assert isinstance(chunks, list)
        assert len(chunks) > 0, "Should generate chunks"
        
        # Check for semantic enhancement
        semantic_enhanced_count = 0
        for chunk in chunks:
            if isinstance(chunk, dict) and 'metadata' in chunk:
                metadata = chunk['metadata']
                if metadata.get('semantic_analysis') is True:
                    semantic_enhanced_count += 1
                    
                    # Verify semantic metadata fields exist
                    assert 'semantic_elements' in metadata
                    assert 'semantic_relationships' in metadata
                    assert 'semantic_clusters' in metadata
                    
                    # Verify data types
                    assert isinstance(metadata['semantic_elements'], dict)
                    assert isinstance(metadata['semantic_relationships'], list)
                    assert isinstance(metadata['semantic_clusters'], list)
        
        assert semantic_enhanced_count > 0, "Should have semantically enhanced chunks"

    def test_semantic_metadata_content_matching(self, enhanced_chunker, sample_c_code):
        """Test that semantic metadata includes relevant element matching"""
        chunks = enhanced_chunker("test_memory.c", sample_c_code, "c_cpp")
        
        relevant_elements_found = False
        for chunk in chunks:
            if isinstance(chunk, dict) and 'metadata' in chunk:
                metadata = chunk['metadata']
                if 'relevant_semantic_elements' in metadata:
                    relevant_elements_found = True
                    assert isinstance(metadata['relevant_semantic_elements'], dict)
                    
                    # If relevant elements exist, they should be meaningful
                    if len(metadata['relevant_semantic_elements']) > 0:
                        # Check that element names appear in chunk content
                        chunk_content = chunk.get('content', '')
                        for element_name in metadata['relevant_semantic_elements'].keys():
                            # Element name should appear in the chunk content
                            assert element_name in chunk_content or any(
                                part in chunk_content for part in element_name.split('_')
                            ), f"Element {element_name} should be relevant to chunk content"
        
        # At least some chunks should have relevant elements
        assert relevant_elements_found, "Should find relevant semantic elements"

    def test_non_cpp_files_bypass_semantic(self, enhanced_chunker):
        """Test that non-C/C++ files bypass semantic enhancement"""
        python_code = '''
def hello_world():
    print("Hello, World!")

def calculate_sum(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
    result = calculate_sum(5, 3)
    print(f"Sum: {result}")
'''
        
        chunks = enhanced_chunker("test.py", python_code, "python")
        
        # Should generate chunks but without semantic enhancement
        assert len(chunks) > 0
        
        for chunk in chunks:
            if isinstance(chunk, dict) and 'metadata' in chunk:
                metadata = chunk['metadata']
                # Should not have semantic analysis for Python files
                assert metadata.get('semantic_analysis') is not True

    def test_query_processor_enhancement(self, semantic_chunker, sample_c_code):
        """Test enhanced query processing"""
        # First get semantic analysis
        analysis = semantic_chunker.analyze_codebase_semantics({"test_memory.c": sample_c_code})
        
        # Test query processor
        query_processor = EnhancedQueryProcessor(semantic_chunker)
        
        test_queries = [
            "explain memory_alloc function",
            "how to use memory management",
            "what are the memory functions"
        ]
        
        for query in test_queries:
            enhanced = query_processor.enhance_query(query, analysis['elements'])
            
            assert isinstance(enhanced, dict)
            assert 'enhanced_query' in enhanced
            assert 'context_expansion' in enhanced
            
            # Enhanced query should contain additional context or be different from original
            # Note: Some queries might not be enhanced if no relevant elements are found
            assert len(enhanced['enhanced_query']) >= len(query), "Enhanced query should be at least as long as original"

    @pytest.mark.integration
    def test_server_integration_health(self):
        """Test server health and availability"""
        try:
            response = requests.get("http://home-ai-server.local:5002/health", timeout=10)
            assert response.status_code == 200
            
            health_data = response.json()
            # Check for various possible status fields
            status_indicators = [
                health_data.get('status'),
                health_data.get('overall_status'),
                health_data.get('code_analyzer_service')
            ]

            # At least one status indicator should be healthy
            healthy_statuses = ['healthy', 'ok', 'running']
            assert any(status in healthy_statuses for status in status_indicators if status), \
                f"No healthy status found in: {status_indicators}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"Server not available: {e}")

    @pytest.mark.integration
    def test_server_search_endpoint(self):
        """Test server's search endpoint functionality"""
        try:
            # Use the correct parameter format based on API spec
            test_data = {
                "query": "memory allocation functions",
                "codebase_name": "utils",  # API requires codebase_name, not codebase
                "max_results": 5
            }

            response = requests.post(
                "http://home-ai-server.local:5002/search",
                json=test_data,
                timeout=30
            )

            if response.status_code != 200:
                # Try alternative parameter format
                test_data_alt = {
                    "query": "memory allocation functions",
                    "max_results": 5
                }
                response = requests.post(
                    "http://home-ai-server.local:5002/search",
                    json=test_data_alt,
                    timeout=30
                )

            if response.status_code != 200:
                pytest.skip(f"Search endpoint not available: {response.status_code}")

            result = response.json()

            # Verify we got some kind of response
            assert isinstance(result, dict), "Search should return a dictionary"

            # Look for common response patterns
            possible_chunk_keys = ['chunks', 'results', 'data', 'matches']
            chunks = []

            for key in possible_chunk_keys:
                if key in result and isinstance(result[key], list):
                    chunks = result[key]
                    break

            print(f"   Search response keys: {list(result.keys())}")
            print(f"   Found chunks/results: {len(chunks)}")

            # If we got chunks, check for semantic-related content
            if len(chunks) > 0:
                semantic_indicators = 0
                for chunk in chunks[:3]:  # Check first 3 chunks
                    chunk_str = str(chunk).lower()
                    if any(term in chunk_str for term in ['semantic', 'function', 'memory', 'allocation']):
                        semantic_indicators += 1

                print(f"   Chunks with semantic indicators: {semantic_indicators}/{min(len(chunks), 3)}")

        except requests.exceptions.RequestException as e:
            pytest.skip(f"Server not available: {e}")

    @pytest.mark.integration
    def test_server_tools_analyze_codebase(self):
        """Test server's analyze_codebase tool endpoint"""
        try:
            # Use the actual endpoint from API spec
            test_data = {
                "query": "find memory allocation functions",
                "codebase": "utils"
            }

            response = requests.post(
                "http://home-ai-server.local:5002/tools/analyze_codebase",
                json=test_data,
                timeout=30
            )

            if response.status_code != 200:
                pytest.skip(f"Analyze codebase endpoint not available: {response.status_code}")

            result = response.json()

            # Verify we got a meaningful response
            assert isinstance(result, dict), "Should return a dictionary"

            # Look for analysis-related content
            response_str = str(result).lower()
            analysis_indicators = [
                'function', 'memory', 'allocation', 'code', 'analysis',
                'tmwmem', 'semantic', 'context'
            ]

            found_indicators = [ind for ind in analysis_indicators if ind in response_str]

            print(f"   Analysis response keys: {list(result.keys())}")
            print(f"   Analysis indicators found: {found_indicators}")
            print(f"   Response contains analysis content: {len(found_indicators) > 0}")

            # Basic validation
            assert len(str(result)) > 50, "Response should contain substantial content"

        except requests.exceptions.RequestException as e:
            pytest.skip(f"Server not available: {e}")

    @pytest.mark.integration
    def test_server_api_enhance_query(self):
        """Test server's API enhance query endpoint"""
        try:
            # Use the correct parameter format (codebase_name instead of codebase)
            test_data = {
                "query": "explain memory allocation functions",
                "codebase_name": "utils"
            }

            response = requests.post(
                "http://home-ai-server.local:5002/api/v1/enhance_query",
                json=test_data,
                timeout=20
            )

            if response.status_code != 200:
                pytest.skip(f"API enhance query endpoint not available: {response.status_code}")

            result = response.json()

            # Verify we got the expected enhancement response format
            assert isinstance(result, dict), "Should return a dictionary"

            response_keys = list(result.keys())
            print(f"   Enhancement response keys: {response_keys}")

            # Check for expected enhancement response fields
            expected_fields = ['original_query', 'enhanced_query', 'enhancements', 'codebase']
            found_fields = [field for field in expected_fields if field in result]

            print(f"   Expected fields found: {found_fields}")

            # Verify we got meaningful enhancement
            if 'enhanced_query' in result and 'original_query' in result:
                original = result['original_query']
                enhanced = result['enhanced_query']
                print(f"   Original: {original}")
                print(f"   Enhanced: {enhanced}")

                # Enhanced query should contain the original or be meaningfully different
                assert len(enhanced) >= len(original), "Enhanced query should be at least as long as original"

            # Basic validation
            assert len(found_fields) > 0, "Should contain expected enhancement fields"

        except requests.exceptions.RequestException as e:
            pytest.skip(f"Server not available: {e}")

    def test_real_codebase_file(self, enhanced_chunker):
        """Test with real codebase file if available"""
        utils_path = Path("../source_code/utils")  # Relative to unit-tests directory
        if not utils_path.exists():
            utils_path = Path("source_code/utils")  # Alternative path
        
        if not utils_path.exists():
            pytest.skip("Utils codebase not found")
        
        # Find a C file
        c_files = list(utils_path.glob("*.c"))
        if not c_files:
            c_files = list(utils_path.glob("**/*.c"))
        
        if not c_files:
            pytest.skip("No C files found in utils codebase")
        
        test_file = c_files[0]
        
        try:
            with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if len(content) < 200:
                pytest.skip("File too small for meaningful test")
            
            chunks = enhanced_chunker(str(test_file), content, "c_cpp")
            
            assert len(chunks) > 0, "Should generate chunks from real file"
            
            # Count semantic enhancements
            semantic_count = 0
            for chunk in chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    if metadata.get('semantic_analysis') is True:
                        semantic_count += 1
            
            assert semantic_count > 0, "Real C file should have semantic enhancement"
            
        except Exception as e:
            pytest.skip(f"Error processing real file: {e}")

    @pytest.mark.integration
    def test_server_enhanced_search(self):
        """Test server's enhanced search endpoint"""
        try:
            # Use the correct parameter format (codebase_name instead of codebase)
            test_data = {
                "query": "find memory allocation functions",
                "codebase_name": "utils"
            }

            response = requests.post(
                "http://home-ai-server.local:5002/tools/enhanced_search",
                json=test_data,
                timeout=30
            )

            if response.status_code != 200:
                pytest.skip(f"Enhanced search endpoint not available: {response.status_code}")

            result = response.json()

            # Verify we got a meaningful response
            assert isinstance(result, dict), "Enhanced search should return a dictionary"

            response_keys = list(result.keys())
            print(f"   Enhanced search response keys: {response_keys}")

            # The enhanced search returns results in 'result' field (like list_codebases)
            if 'result' in result:
                search_result = result['result']
                print(f"   Search result type: {type(search_result)}")
                print(f"   Search result preview: {str(search_result)[:200]}...")

                # Look for search-related content in the result
                result_text = str(search_result).lower()
                search_indicators = [
                    'function', 'memory', 'allocation', 'tmwmem',
                    'code', 'context', 'found', 'search'
                ]

                found_indicators = [ind for ind in search_indicators if ind in result_text]
                print(f"   Search indicators found: {found_indicators}")

                # Verify we got meaningful search results
                assert len(str(search_result)) > 50, "Search result should contain substantial content"
                assert len(found_indicators) > 0, "Should find search-related content"
            else:
                # Fallback validation for other response formats
                assert len(str(result)) > 50, "Response should contain substantial content"

        except requests.exceptions.RequestException as e:
            pytest.skip(f"Server not available: {e}")

    @pytest.mark.integration
    def test_server_tools_list_codebases(self):
        """Test server's tools/list_codebases endpoint and semantic readiness"""
        try:
            # Use the actual working endpoint
            response = requests.get("http://home-ai-server.local:5002/tools/list_codebases", timeout=15)

            if response.status_code != 200:
                # Try the alternative /codebases endpoint
                response = requests.get("http://home-ai-server.local:5002/codebases", timeout=15)

            if response.status_code != 200:
                pytest.skip(f"List codebases endpoint not available: {response.status_code}")

            result = response.json()

            # Should return codebase information
            assert isinstance(result, (dict, list)), "Should return codebase data"

            print(f"   Codebases response type: {type(result)}")
            print(f"   Response keys: {list(result.keys()) if isinstance(result, dict) else 'N/A (list)'}")

            # Handle the actual response format - it's a formatted string in 'result' field
            if isinstance(result, dict) and 'result' in result:
                result_text = result['result']
                print(f"   Response text preview: {result_text[:200]}...")

                # Parse the formatted text to extract codebase information
                lines = result_text.split('\n')
                codebases_found = []
                utils_found = False
                c_cpp_codebases = 0

                for line in lines:
                    # Look for codebase names (lines starting with ** and containing codebase names)
                    if line.strip().startswith('**') and ('utils' in line.lower() or 'modbus' in line.lower() or 'z80emu' in line.lower()):
                        codebase_name = line.strip()
                        codebases_found.append(codebase_name)

                        if 'utils' in line.lower():
                            utils_found = True

                        # Check if this codebase has C/C++ languages
                        if any(lang in line.lower() for lang in ['c,', 'c++', 'c/c++']):
                            c_cpp_codebases += 1

                    # Also look for language information in subsequent lines
                    elif 'Languages:' in line and any(lang in line for lang in ['C,', 'C++', 'C/C++']):
                        c_cpp_codebases += 1

                print(f"   Found {len(codebases_found)} codebases in response")
                print(f"   Codebases: {[cb[:50] for cb in codebases_found]}")
                print(f"   Utils codebase found: {utils_found}")
                print(f"   C/C++ suitable codebases: {c_cpp_codebases}")

                # Basic validation - we should find at least some codebase information
                assert len(codebases_found) > 0 or 'utils' in result_text.lower(), "Should find codebase information in response"

            else:
                # Fallback for other response formats
                codebases = []
                if isinstance(result, list):
                    codebases = result
                elif isinstance(result, dict):
                    for key in ['codebases', 'available_codebases', 'data', 'results']:
                        if key in result and isinstance(result[key], list):
                            codebases = result[key]
                            break

                print(f"   Fallback: Found {len(codebases)} codebases")
                assert len(codebases) > 0, "Should have at least one codebase available"

        except requests.exceptions.RequestException:
            pytest.skip(f"Server not available: {e}")

    def test_tree_sitter_chunker_basic_vs_semantic(self):
        """Test comparison between basic and semantic chunking methods"""
        # Sample C code for testing
        sample_code = '''
#include <stdio.h>
#include <stdlib.h>

// Memory management functions
void* tmwmem_alloc(size_t size) {
    void* ptr = malloc(size);
    if (ptr == NULL) {
        fprintf(stderr, "Memory allocation failed\\n");
        return NULL;
    }
    return ptr;
}

void tmwmem_free(void* ptr) {
    if (ptr != NULL) {
        free(ptr);
    }
}

// String utility functions
int tmwstr_length(const char* str) {
    if (str == NULL) return 0;

    int len = 0;
    while (str[len] != '\\0') {
        len++;
    }
    return len;
}

char* tmwstr_copy(const char* src) {
    if (src == NULL) return NULL;

    int len = tmwstr_length(src);
    char* dest = (char*)tmwmem_alloc(len + 1);

    if (dest != NULL) {
        for (int i = 0; i <= len; i++) {
            dest[i] = src[i];
        }
    }

    return dest;
}

// Main function
int main() {
    char* test_str = tmwstr_copy("Hello, World!");
    if (test_str != NULL) {
        printf("String: %s, Length: %d\\n", test_str, tmwstr_length(test_str));
        tmwmem_free(test_str);
    }
    return 0;
}
'''

        chunker = TreeSitterChunker()

        # Test basic chunking
        basic_chunks = chunker.chunk_file("test.c", sample_code, "c_cpp")
        assert len(basic_chunks) > 0, "Should generate basic chunks"

        # Test semantic chunking
        semantic_chunks = chunker.chunk_file_with_semantics("test.c", sample_code, "c_cpp")
        assert len(semantic_chunks) > 0, "Should generate semantic chunks"

        # Count semantic enhancements
        semantic_enhanced_count = sum(1 for chunk in semantic_chunks
                                    if chunk.get('semantic_enhanced', False))

        print(f"   Basic chunks: {len(basic_chunks)}")
        print(f"   Semantic chunks: {len(semantic_chunks)}")
        print(f"   Semantically enhanced: {semantic_enhanced_count}")

        # For C/C++ files over 1000 chars, should have semantic enhancement
        if len(sample_code) > 1000:
            assert semantic_enhanced_count > 0, "Should have semantic enhancement for substantial C/C++ files"

        # Verify chunk structure
        for chunk in semantic_chunks:
            assert 'content' in chunk, "Chunk should have content"
            assert 'file_path' in chunk, "Chunk should have file_path"
            assert 'chunk_type' in chunk, "Chunk should have chunk_type"
            assert 'function_name' in chunk, "Chunk should have function_name"

    def test_semantic_chunking_with_different_languages(self):
        """Test semantic chunking behavior with different programming languages"""
        chunker = TreeSitterChunker()

        # Python code (should not get semantic enhancement)
        python_code = '''
def hello_world():
    print("Hello, World!")

class TestClass:
    def __init__(self):
        self.value = 42

    def get_value(self):
        return self.value

if __name__ == "__main__":
    hello_world()
    test = TestClass()
    print(test.get_value())
'''

        python_chunks = chunker.chunk_file_with_semantics("test.py", python_code, "python")
        python_enhanced = sum(1 for chunk in python_chunks if chunk.get('semantic_enhanced', False))

        assert len(python_chunks) > 0, "Should generate Python chunks"
        assert python_enhanced == 0, "Python files should not get semantic enhancement"

        # JavaScript code (should not get semantic enhancement)
        js_code = '''
function calculateSum(a, b) {
    return a + b;
}

class Calculator {
    constructor() {
        this.result = 0;
    }

    add(value) {
        this.result += value;
        return this;
    }

    getResult() {
        return this.result;
    }
}

const calc = new Calculator();
console.log(calc.add(5).add(3).getResult());
'''

        js_chunks = chunker.chunk_file_with_semantics("test.js", js_code, "javascript")
        js_enhanced = sum(1 for chunk in js_chunks if chunk.get('semantic_enhanced', False))

        assert len(js_chunks) > 0, "Should generate JavaScript chunks"
        assert js_enhanced == 0, "JavaScript files should not get semantic enhancement"

        print(f"   Python chunks: {len(python_chunks)}, Enhanced: {python_enhanced}")
        print(f"   JavaScript chunks: {len(js_chunks)}, Enhanced: {js_enhanced}")

    def test_framework_integration_semantic_chunking(self):
        """Test that the framework integration properly uses semantic chunking"""
        # This test verifies that our integration changes are working
        try:
            system = IntegratedCodeAnalysisSystem()

            # Verify the system has the necessary components
            assert hasattr(system, 'framework'), "System should have framework"
            assert hasattr(system, 'chunk_registry'), "System should have chunk registry"
            assert hasattr(system, 'pipeline'), "System should have pipeline"

            print("   Framework integration system initialized successfully")
            print("   ✅ Semantic chunking integration is properly configured")

        except Exception as e:
            pytest.fail(f"Framework integration failed: {e}")

if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s"])
