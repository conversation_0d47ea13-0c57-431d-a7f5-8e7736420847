#!/usr/bin/env python3
"""
Verify deployment files and compare local vs remote server files.
This script helps ensure all necessary files are present after testing reorganization.
"""

import os
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Set, Optional


class DeploymentFileVerifier:
    """Verifies deployment files and compares local vs remote."""
    
    def __init__(self):
        self.local_root = Path.cwd()
        self.remote_host = "home-ai-server"
        self.remote_user = "fvaneijk"
        self.remote_path = "/home/<USER>/home-ai-system/code_analyzer_server"
        
        # Essential files that must be present for the system to work
        self.essential_files = {
            # Core application files
            'main.py': 'Main application entry point',
            'framework_integration.py': 'Framework integration system',
            'language_framework.py': 'Language processing framework',
            'language_processors.py': 'Language-specific processors',
            'language_registry.py': 'Language registry system',
            'gpu_infrastructure.py': 'GPU infrastructure management',
            'processing_pipeline.py': 'Processing pipeline system',
            'chunk_system.py': 'Code chunking system',
            'vector_db_creator.py': 'Vector database creation',
            'open_webui_code_analyzer_tool.py': 'OpenWebUI integration tool',
            'intent_detection_service.py': 'Intent detection service',
            'semantic_patterns.py': 'Semantic pattern matching',
            'metta_processor.py': 'Metta language processor',
            'tree_sitter_chunker.py': 'Tree-sitter based chunking',
            'embedding_config.py': 'Embedding configuration',
            'code_preprocessor.py': 'Code preprocessing system',
            
            # Configuration files
            'requirements.txt': 'Python dependencies',
            'docker-compose.yml': 'Docker composition configuration',
            'Dockerfile': 'Docker container configuration',
            'pytest.ini': 'Testing configuration',
            
            # Configuration directories
            'config/intent_detection.json5': 'Intent detection configuration',
            
            # Unit test framework
            'unit-tests/conftest.py': 'Test configuration',
            'unit-tests/test_analysis_endpoints.py': 'Analysis endpoint tests',
            'unit-tests/test_basic_connectivity.py': 'Basic connectivity tests',
            'unit-tests/test_codebase_management.py': 'Codebase management tests',
            'unit-tests/test_configuration.py': 'Configuration tests',
            'unit-tests/test_data_quality.py': 'Data quality tests',
            'unit-tests/test_deployment_features.py': 'Deployment feature tests',
            'unit-tests/test_framework_integration.py': 'Framework integration tests',
            'unit-tests/test_intent_detection.py': 'Intent detection tests',
            'unit-tests/test_language_support.py': 'Language support tests',
            'unit-tests/test_openwebui_integration.py': 'OpenWebUI integration tests',
            'unit-tests/test_performance.py': 'Performance tests',
            'unit-tests/test_search_functionality.py': 'Search functionality tests',
            'unit-tests/test_server_health.py': 'Server health tests',
            'unit-tests/test_simple_verification.py': 'Simple verification tests',
            
            # Execution scripts
            'run_unit_tests.bat': 'Windows test runner',
            'run_unit_tests.sh': 'Linux test runner'
        }
        
        # Optional files that are good to have but not critical
        self.optional_files = {
            'templates/dashboard.html': 'Web dashboard template',
            'docs/': 'Documentation directory',
            'test_reports/': 'Test reports directory',
            'chroma_db/': 'ChromaDB storage directory',
            'source_code/': 'Source code repositories'
        }
    
    def check_local_files(self) -> Dict[str, bool]:
        """Check which essential files are present locally."""
        print("🔍 Checking Local Files")
        print("=" * 50)
        
        local_status = {}
        missing_files = []
        present_files = []
        
        for file_path, description in self.essential_files.items():
            full_path = self.local_root / file_path
            exists = full_path.exists()
            local_status[file_path] = exists
            
            if exists:
                present_files.append(file_path)
                print(f"✅ {file_path} - {description}")
            else:
                missing_files.append(file_path)
                print(f"❌ {file_path} - {description} (MISSING)")
        
        print(f"\n📊 Local File Status:")
        print(f"   Present: {len(present_files)}/{len(self.essential_files)}")
        print(f"   Missing: {len(missing_files)}")
        
        if missing_files:
            print(f"\n⚠️ Missing Essential Files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
        
        return local_status
    
    def generate_remote_check_script(self) -> str:
        """Generate a script to check files on the remote server."""
        script_content = f'''#!/bin/bash
# Remote file verification script for {self.remote_path}

echo "🔍 Checking Remote Files on {self.remote_host}"
echo "=================================================="

cd {self.remote_path} || exit 1

echo "📁 Current directory: $(pwd)"
echo ""

# Check essential files
echo "🔧 Essential Application Files:"
'''
        
        for file_path, description in self.essential_files.items():
            script_content += f'''
if [ -e "{file_path}" ]; then
    echo "✅ {file_path} - {description}"
else
    echo "❌ {file_path} - {description} (MISSING)"
fi'''
        
        script_content += '''

echo ""
echo "📊 Directory Structure:"
find . -maxdepth 2 -type f -name "*.py" | head -20
echo ""

echo "📦 Docker Files:"
ls -la docker-compose.yml Dockerfile 2>/dev/null || echo "Docker files not found"
echo ""

echo "🧪 Unit Tests:"
ls -la unit-tests/ 2>/dev/null || echo "Unit tests directory not found"
echo ""

echo "⚙️ Configuration:"
ls -la config/ requirements.txt pytest.ini 2>/dev/null || echo "Configuration files not found"
echo ""

echo "🗂️ Source Code Repositories:"
ls -la source_code/ 2>/dev/null || echo "Source code directory not found"
'''
        
        return script_content
    
    def create_sync_script(self, missing_files: List[str]) -> str:
        """Create a script to sync missing files to the remote server."""
        if not missing_files:
            return "# No files need to be synced - all essential files are present locally"
        
        script_content = f'''#!/bin/bash
# Sync missing files to {self.remote_host}

echo "🚀 Syncing Missing Files to {self.remote_host}"
echo "=============================================="

# Ensure remote directory exists
ssh {self.remote_user}@{self.remote_host} "mkdir -p {self.remote_path}"

# Sync missing files
'''
        
        for file_path in missing_files:
            local_path = str(self.local_root / file_path)
            remote_full_path = f"{self.remote_path}/{file_path}"
            
            script_content += f'''
echo "📤 Syncing {file_path}..."
if [ -e "{local_path}" ]; then
    scp "{local_path}" {self.remote_user}@{self.remote_host}:{remote_full_path}
    echo "✅ Synced {file_path}"
else
    echo "❌ Local file {file_path} not found - cannot sync"
fi
'''
        
        script_content += '''
echo ""
echo "✅ Sync completed!"
echo "🔍 Run the remote check script to verify files"
'''
        
        return script_content
    
    def run_verification(self):
        """Run the complete verification process."""
        print("🔧 OpenWebUI RAG Code Server - Deployment File Verification")
        print("=" * 70)
        print()
        
        # Check local files
        local_status = self.check_local_files()
        
        # Identify missing files
        missing_files = [f for f, exists in local_status.items() if not exists]
        
        print("\n" + "=" * 50)
        
        # Generate remote check script
        remote_script = self.generate_remote_check_script()
        remote_script_path = self.local_root / "check_remote_files.sh"
        
        with open(remote_script_path, 'w', encoding='utf-8', newline='\n') as f:
            f.write(remote_script)
        
        print(f"📝 Generated remote check script: {remote_script_path}")
        print(f"   Run this on {self.remote_host}: bash check_remote_files.sh")
        
        # Generate sync script if needed
        if missing_files:
            sync_script = self.create_sync_script(missing_files)
            sync_script_path = self.local_root / "sync_to_remote.sh"
            
            with open(sync_script_path, 'w', encoding='utf-8', newline='\n') as f:
                f.write(sync_script)
            
            print(f"📤 Generated sync script: {sync_script_path}")
            print(f"   Run this locally: bash sync_to_remote.sh")
        
        print("\n🔍 Manual Verification Steps:")
        print("1. Copy check_remote_files.sh to the remote server")
        print(f"2. SSH to {self.remote_user}@{self.remote_host}")
        print(f"3. cd {self.remote_path}")
        print("4. bash check_remote_files.sh")
        print("5. Compare results with local file status above")
        
        if missing_files:
            print("\n⚠️ Missing Files Detected:")
            print("6. Run sync_to_remote.sh to copy missing files")
            print("7. Re-run check_remote_files.sh to verify sync")
        
        print(f"\n📊 Summary:")
        print(f"   Essential files present locally: {len(self.essential_files) - len(missing_files)}/{len(self.essential_files)}")
        print(f"   Files needing sync: {len(missing_files)}")
        
        if len(missing_files) == 0:
            print("✅ All essential files are present locally!")
        else:
            print("⚠️ Some essential files are missing and need to be synced")
        
        return local_status


def main():
    """Main entry point."""
    verifier = DeploymentFileVerifier()
    verifier.run_verification()


if __name__ == "__main__":
    main()
