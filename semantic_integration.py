#!/usr/bin/env python3
"""
Semantic Chunking Integration Module
Integrates semantic chunking enhancements into the existing code analysis system
"""

import sys
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add current directory to path for imports
sys.path.append('.')

from semantic_chunking_enhancement import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EnhancedQueryProcessor, ContextAwareRanker
from tree_sitter_chunker import TreeSitterChunker

class SemanticCodeAnalyzer:
    """Enhanced code analyzer with semantic chunking capabilities"""
    
    def __init__(self):
        self.semantic_chunker = SemanticChunker()
        self.query_processor = EnhancedQueryProcessor(self.semantic_chunker)
        self.ranker = ContextAwareRanker()
        self.tree_sitter_chunker = TreeSitterChunker()
        
        # Cache for semantic analysis results
        self.semantic_cache: Dict[str, Dict[str, Any]] = {}
    
    def process_codebase_with_semantics(self, codebase_path: str, file_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Process entire codebase with semantic analysis"""
        print("🧠 [SEMANTIC] Starting semantic codebase analysis...")
        
        # Step 1: Collect all files
        file_contents = self._collect_codebase_files(codebase_path, file_patterns)
        
        # Step 2: Perform semantic analysis
        semantic_analysis = self.semantic_chunker.analyze_codebase_semantics(file_contents)
        
        # Step 3: Create enhanced chunks
        enhanced_chunks = self._create_enhanced_chunks(semantic_analysis, file_contents)
        
        # Step 4: Cache results
        codebase_name = Path(codebase_path).name
        self.semantic_cache[codebase_name] = {
            'semantic_analysis': semantic_analysis,
            'enhanced_chunks': enhanced_chunks,
            'file_contents': file_contents
        }
        
        return {
            'success': True,
            'codebase_name': codebase_name,
            'chunks': enhanced_chunks,
            'semantic_elements': len(semantic_analysis['elements']),
            'semantic_clusters': len(semantic_analysis['clusters']),
            'total_chunks': len(enhanced_chunks)
        }
    
    def enhance_search_query(self, query: str, codebase_name: str) -> Dict[str, Any]:
        """Enhance search query with semantic context"""
        if codebase_name not in self.semantic_cache:
            return {'enhanced_query': query, 'context_expansion': []}
        
        semantic_data = self.semantic_cache[codebase_name]
        elements = semantic_data['semantic_analysis']['elements']
        
        return self.query_processor.enhance_query(query, elements)
    
    def rank_search_results(self, results: List[Dict[str, Any]], query_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Rank search results using context-aware ranking"""
        return self.ranker.rank_search_results(results, query_context)
    
    def get_multi_level_context(self, element_name: str, codebase_name: str, context_level: int = 4) -> Dict[str, Any]:
        """Get multi-level context for a specific element"""
        if codebase_name not in self.semantic_cache:
            return {'error': 'Codebase not analyzed'}
        
        semantic_data = self.semantic_cache[codebase_name]
        elements = semantic_data['semantic_analysis']['elements']
        
        if element_name not in elements:
            return {'error': f'Element {element_name} not found'}
        
        element = elements[element_name]
        context_levels = {}
        
        # Level 1: Function signature + documentation
        if context_level >= 1:
            context_levels['signature'] = {
                'name': element.name,
                'type': element.element_type,
                'documentation': element.documentation,
                'content': element.content
            }
        
        # Level 2: Data structures and macros used
        if context_level >= 2:
            structures = []
            macros = []
            
            if element.dependencies:
                for dep in element.dependencies:
                    if dep in elements:
                        dep_element = elements[dep]
                        if dep_element.element_type == 'struct':
                            structures.append({
                                'name': dep,
                                'content': dep_element.content,
                                'documentation': dep_element.documentation
                            })
                        elif dep_element.element_type == 'macro':
                            macros.append({
                                'name': dep,
                                'content': dep_element.content
                            })
            
            context_levels['structures'] = structures
            context_levels['macros'] = macros
        
        # Level 3: Usage patterns and examples
        if context_level >= 3:
            usage_examples = []
            
            for name, other_element in elements.items():
                if (other_element.dependencies and 
                    element_name in other_element.dependencies and
                    other_element.element_type == 'function'):
                    usage_examples.append({
                        'name': name,
                        'content': other_element.content,
                        'file_path': other_element.file_path
                    })
            
            context_levels['usage_examples'] = usage_examples
        
        # Level 4: Configuration options that affect behavior
        if context_level >= 4:
            config_options = []
            
            if element.dependencies:
                for dep in element.dependencies:
                    if (dep in elements and
                        elements[dep].element_type == 'macro' and
                        'configuration' in elements[dep].semantic_tags):
                        config_options.append({
                            'name': dep,
                            'content': elements[dep].content,
                            'file_path': elements[dep].file_path
                        })
            
            context_levels['configuration'] = config_options
        
        return {
            'element_name': element_name,
            'context_levels': context_levels,
            'total_levels': context_level
        }
    
    def _collect_codebase_files(self, codebase_path: str, file_patterns: Optional[List[str]] = None) -> Dict[str, str]:
        """Collect all relevant files from codebase"""
        file_contents = {}
        
        # Default patterns for C/C++ files
        if not file_patterns:
            file_patterns = ['*.c', '*.cpp', '*.h', '*.hpp']
        
        codebase_path_obj = Path(codebase_path)
        
        for pattern in file_patterns:
            for file_path in codebase_path_obj.rglob(pattern):
                if file_path.is_file():
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            relative_path = str(file_path.relative_to(codebase_path_obj))
                            file_contents[relative_path] = content
                    except Exception as e:
                        print(f"⚠️ [SEMANTIC] Error reading {file_path}: {e}")
        
        print(f"📁 [SEMANTIC] Collected {len(file_contents)} files for analysis")
        return file_contents
    
    def _create_enhanced_chunks(self, semantic_analysis: Dict[str, Any], file_contents: Dict[str, str]) -> List[Dict[str, Any]]:
        """Create enhanced chunks combining semantic and tree-sitter analysis"""
        enhanced_chunks = []
        
        # Use semantic clusters as primary chunks
        for cluster_chunk in semantic_analysis['enhanced_chunks']:
            if cluster_chunk['metadata']['chunk_type'] == 'semantic_cluster':
                enhanced_chunks.append(self._format_chunk_for_vector_db(cluster_chunk))
        
        # Add individual elements that weren't clustered
        for individual_chunk in semantic_analysis['enhanced_chunks']:
            if individual_chunk['metadata']['chunk_type'] == 'individual_element':
                enhanced_chunks.append(self._format_chunk_for_vector_db(individual_chunk))
        
        # Fill gaps with tree-sitter chunks for files not covered by semantic analysis
        covered_files = set()
        for chunk in enhanced_chunks:
            if 'file_paths' in chunk['metadata']:
                covered_files.update(chunk['metadata']['file_paths'])
            elif 'file_path' in chunk['metadata']:
                covered_files.add(chunk['metadata']['file_path'])
        
        for file_path, content in file_contents.items():
            if file_path not in covered_files:
                # Use tree-sitter for uncovered files
                tree_chunks = self.tree_sitter_chunker.chunk_file(file_path, content, 'c_cpp')
                for tree_chunk in tree_chunks:
                    enhanced_chunks.append(self._format_chunk_for_vector_db({
                        'content': tree_chunk['content'],
                        'metadata': {
                            'chunk_type': 'tree_sitter_fallback',
                            'file_path': file_path,
                            'context_level': 'basic'
                        }
                    }))
        
        print(f"📦 [SEMANTIC] Created {len(enhanced_chunks)} enhanced chunks")
        return enhanced_chunks
    
    def _format_chunk_for_vector_db(self, chunk: Dict[str, Any]) -> Dict[str, Any]:
        """Format chunk for vector database storage"""
        return {
            'content': chunk['content'],
            'metadata': {
                **chunk['metadata'],
                'enhanced_semantic': True,
                'processing_method': 'semantic_enhanced'
            }
        }

def integrate_with_existing_system():
    """Integration function to enhance the existing code analysis system"""
    
    def create_semantic_analyzer_wrapper(existing_service):
        """Wrap existing service with semantic capabilities"""
        
        semantic_analyzer = SemanticCodeAnalyzer()
        
        # Enhance the process_codebase method
        original_process = existing_service.process_codebase
        
        async def enhanced_process_codebase(codebase_name: str, exclude_dirs: Optional[List[str]] = None):
            """Enhanced codebase processing with semantic analysis"""
            
            # First run the original processing
            original_result = await original_process(codebase_name, exclude_dirs)
            
            # Then enhance with semantic analysis for C/C++ codebases
            codebase_path = f"./source_code/{codebase_name}"
            if Path(codebase_path).exists():
                try:
                    semantic_result = semantic_analyzer.process_codebase_with_semantics(codebase_path)
                    
                    # Merge results
                    if semantic_result['success']:
                        print(f"✅ [SEMANTIC] Enhanced {codebase_name} with {semantic_result['semantic_elements']} elements")
                        return {
                            **original_result,
                            'semantic_enhanced': True,
                            'semantic_elements': semantic_result['semantic_elements'],
                            'semantic_clusters': semantic_result['semantic_clusters']
                        }
                except Exception as e:
                    print(f"⚠️ [SEMANTIC] Semantic enhancement failed for {codebase_name}: {e}")
            
            return original_result
        
        # Enhance the search method
        original_search = existing_service.search
        
        def enhanced_search(query: str, codebase_name: str, n_results: int = 10, **kwargs):
            """Enhanced search with semantic query processing"""
            
            # Enhance query if semantic data is available
            enhanced_query_data = semantic_analyzer.enhance_search_query(query, codebase_name)
            
            if enhanced_query_data.get('context_expansion'):
                print(f"🧠 [SEMANTIC] Enhanced query with {len(enhanced_query_data['context_expansion'])} context terms")
                search_query = enhanced_query_data['enhanced_query']
            else:
                search_query = query
            
            # Perform original search
            results = original_search(search_query, codebase_name, n_results, **kwargs)
            
            # Enhance ranking if we have semantic context
            if enhanced_query_data.get('context_expansion'):
                results = semantic_analyzer.rank_search_results(results, enhanced_query_data)
                print(f"🎯 [SEMANTIC] Re-ranked {len(results)} results using context-aware ranking")
            
            return results
        
        # Replace methods
        existing_service.process_codebase = enhanced_process_codebase
        existing_service.search = enhanced_search
        existing_service.semantic_analyzer = semantic_analyzer
        
        return existing_service
    
    return create_semantic_analyzer_wrapper

if __name__ == "__main__":
    # Test the semantic analyzer
    analyzer = SemanticCodeAnalyzer()
    
    # Test with utils codebase
    result = analyzer.process_codebase_with_semantics("./source_code/utils")
    print(f"Processed utils codebase: {result['success']}")
    print(f"Elements: {result['semantic_elements']}, Clusters: {result['semantic_clusters']}")
    
    # Test query enhancement
    enhanced = analyzer.enhance_search_query("explain tmwmem_lowFree function", "utils")
    print(f"Enhanced query: {enhanced.get('enhanced_query', 'No enhancement')}")
    
    # Test multi-level context
    context = analyzer.get_multi_level_context("tmwmem_lowFree", "utils", context_level=4)
    if 'context_levels' in context:
        print(f"Multi-level context has {len(context['context_levels'])} levels")
