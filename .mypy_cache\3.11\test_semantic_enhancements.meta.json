{"data_mtime": 1753185489, "dep_lines": [7, 8, 9, 10, 11, 16, 50, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["sys", "os", "time", "typing", "pathlib", "semantic_integration", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "ae8d196281a1c2a41fd4fd6ebd1b93cdbaa65471", "id": "test_semantic_enhancements", "ignore_all": false, "interface_hash": "7c2b84467a5351545019a4c0f07d660817399351", "mtime": 1753185488, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_semantic_enhancements.py", "plugin_data": null, "size": 11118, "suppressed": [], "version_id": "1.15.0"}