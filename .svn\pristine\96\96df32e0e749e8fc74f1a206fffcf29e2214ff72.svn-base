# 🧪 Comprehensive Unit Testing Framework

## Overview

A world-class unit testing framework has been implemented for the OpenWebUI RAG Code Server, providing comprehensive coverage of all functionality including semantic analysis, framework integration, and server endpoints. The testing framework uses pytest and is fully integrated with Visual Studio Code.

## 📊 Testing Framework Statistics

### **Current Test Coverage**
- **Total Tests**: 81 comprehensive tests (65% increase from original 49)
- **Success Rate**: 99% (80 passed, 1 appropriately skipped)
- **Test Modules**: 11 focused unit test modules
- **Execution**: Single command (`python -m pytest unit-tests/`)

### **Test Categories**
- **Core Functionality**: 49 tests (server health, connectivity, management)
- **Enhanced Features**: 32 tests (language support, performance, OpenWebUI)
- **Semantic Analysis**: 13 tests (semantic chunking integration)

## Directory Structure

```
unit-tests/
├── __init__.py                           # Package initialization
├── conftest.py                           # Pytest configuration and fixtures
├── test_basic_connectivity.py            # Basic connectivity tests (3 tests)
├── test_server_health.py                 # Server health endpoint tests (5 tests)
├── test_codebase_management.py           # Codebase management tests (6 tests)
├── test_search_functionality.py          # Search and query tests (6 tests)
├── test_framework_integration.py         # Framework integration tests (8 tests)
├── test_intent_detection.py              # Intent detection tests (5 tests)
├── test_analysis_endpoints.py            # Analysis service tests (5 tests)
├── test_simple_verification.py           # Simple verification tests (11 tests)
├── test_language_support.py              # Language support tests (11 tests)
├── test_performance.py                   # Performance benchmarking (9 tests)
├── test_openwebui_integration.py         # OpenWebUI integration tests (12 tests)
├── test_semantic_integration.py          # Semantic analysis tests (13 tests)
├── test_semantic_chunking_integration.py # Semantic chunking tests (6 tests)
├── run_tests.py                          # Test runner script
└── README.md                             # Detailed testing documentation
```

## 🚀 Key Features

### 1. **Visual Studio Code Integration**
- Pytest is configured as the default test framework
- Test discovery is automatic
- Debug configurations are available
- Tests can be run individually or in groups
- Full IDE support for test running/debugging

### 2. **Comprehensive Test Coverage**
- **Health Tests**: Server connectivity, health endpoints, status checks
- **Codebase Management**: List, select, session management, statistics
- **Search Functionality**: Enhanced search, context retrieval, legacy compatibility
- **Framework Integration**: GPU infrastructure, framework status, query enhancement
- **Intent Detection**: Query classification, configuration management
- **Analysis Endpoints**: Analysis service functionality
- **Language Support**: 45+ programming language validation
- **Performance Testing**: Response time benchmarking, concurrent requests
- **OpenWebUI Integration**: Tool compatibility and workflow testing
- **Semantic Analysis**: Semantic chunking and enhancement validation

### 3. **Advanced Testing Capabilities**
- **Semantic Enhancement Testing**: Validates C/C++ semantic chunking integration
- **Multi-language Testing**: Tests across 45+ supported programming languages
- **Performance Benchmarking**: Response time and load testing capabilities
- **Integration Testing**: Real server integration with proper error handling
- **Regression Prevention**: Comprehensive test suite catches issues early

### 4. **Flexible Test Execution**
- Run all tests or specific test files
- Filter tests by markers (health, search, framework, semantic, etc.)
- Verbose output and coverage reporting
- Cross-platform support (Windows/Linux/Mac)
- Async/await support for modern testing patterns

## Running Tests

### From Visual Studio Code
1. **Test Explorer**: Use the built-in test explorer panel
2. **Debug Menu**: Select "Run Unit Tests" from the debug configurations
3. **Command Palette**: Use `Python: Run All Tests`

### From Command Line
```bash
# Run all tests with verbose output
python unit-tests/run_tests.py --verbose

# Run specific test file
python unit-tests/run_tests.py --pattern test_server_health.py

# Run tests with specific markers
python unit-tests/run_tests.py --markers "health"

# Run semantic analysis tests
python unit-tests/run_tests.py --markers "semantic"

# Run performance tests
python unit-tests/run_tests.py --markers "slow"

# Run with coverage reporting
python unit-tests/run_tests.py --coverage
```

### Using pytest Directly
```bash
# Run all tests (81 comprehensive tests)
python -m pytest unit-tests -v

# Run specific test categories
python -m pytest unit-tests -m "health" -v
python -m pytest unit-tests -m "semantic" -v
python -m pytest unit-tests -m "framework" -v

# Run specific test files
python -m pytest unit-tests/test_semantic_integration.py -v
python -m pytest unit-tests/test_language_support.py -v
python -m pytest unit-tests/test_performance.py -v

# Run with coverage
python -m pytest unit-tests --cov=. --cov-report=html -v
```

### Quick Start Scripts
- **Windows**: `run_unit_tests.bat`
- **Linux/Mac**: `run_unit_tests.sh`

## Prerequisites

1. **Server Running**: The code analysis server must be running on home-ai-server.local:5002
2. **Dependencies**: All required packages are listed in `requirements.txt`
3. **Python Environment**: Python 3.7+ with pytest and httpx

## Test Configuration

### Server Settings
- Base URL: `http://home-ai-server.local:5002`
- Timeout: 30 seconds
- Sample codebase: `utils`

### Test Markers
- `health` - Health check tests
- `search` - Search functionality tests
- `framework` - Framework integration tests
- `codebase` - Codebase management tests
- `intent` - Intent detection tests
- `analysis` - Analysis endpoints tests
- `integration` - Server integration tests
- `unit` - Local unit tests
- `slow` - Performance and load tests
- `semantic` - Semantic analysis tests

## Key Test Files

### `test_basic_connectivity.py`
Simple connectivity tests that verify the server is running and accessible. Run this first to ensure basic functionality.

### `test_server_health.py`
Comprehensive health endpoint testing including:
- Root endpoint validation
- Health status checks
- Detailed health information
- Enhanced features documentation

### `test_codebase_management.py`
Tests for codebase operations:
- Listing available codebases
- Selecting and managing codebases
- Session management
- Statistics retrieval

### `test_search_functionality.py`
Search and query testing:
- Enhanced search capabilities
- Optimized context retrieval
- Legacy endpoint compatibility
- Stateless operations

### `test_semantic_integration.py` ✨
**Comprehensive semantic analysis testing** (13 tests):
- **Local Semantic Analysis**: SemanticChunker initialization and basic analysis
- **Integration Testing**: TreeSitterChunker with semantic enhancement
- **Server Integration**: Real server semantic functionality validation
- **Language Filtering**: C/C++ enhancement vs other language bypass
- **Metadata Validation**: Semantic enhancement flags and context levels
- **Real Codebase Testing**: Actual file processing from utils codebase

### `test_semantic_chunking_integration.py` ✨
**Dedicated semantic chunking tests** (6 tests):
- **Framework Integration**: Semantic chunking in processing pipeline
- **Metadata Preservation**: Semantic enhancement metadata structure
- **Fallback Behavior**: Small files and non-C/C++ language handling
- **Direct Functionality**: SemanticChunker direct testing
- **Context Level Validation**: `signature`, `multi_level`, `enhanced` levels

### `test_language_support.py` 🌐
**Multi-language validation** (11 tests):
- **45+ Language Support**: Validates all supported programming languages
- **Language Detection**: Tests language identification capabilities
- **Multi-language Codebases**: Verifies handling of mixed-language projects
- **Extension Mapping**: Tests file extension to language mapping

### `test_performance.py` ⚡
**Performance benchmarking** (9 tests):
- **Response Time Testing**: Validates API response times
- **Concurrent Request Testing**: Load testing capabilities
- **Performance Consistency**: Ensures stable performance under load
- **Benchmark Reporting**: Performance metrics collection

### `test_openwebui_integration.py` 🔧
**OpenWebUI integration testing** (12 tests):
- **Tool Compatibility**: OpenWebUI tool endpoint validation
- **Integration Workflow**: End-to-end OpenWebUI integration testing
- **API Compatibility**: Ensures OpenWebUI API compatibility

## 🧠 Semantic Analysis Testing

### **Semantic Enhancement Validation**
The testing framework includes comprehensive validation of semantic analysis capabilities:

#### **Core Semantic Tests**
- ✅ **Semantic Chunker Initialization**: Verifies SemanticChunker initializes properly
- ✅ **Basic Semantic Analysis**: Tests semantic analysis with sample C code
- ✅ **Integration with TreeSitter**: Tests semantic enhancement integration
- ✅ **Metadata Content Matching**: Verifies semantic metadata includes relevant elements
- ✅ **Language Filtering**: Ensures non-C/C++ files bypass semantic enhancement
- ✅ **Real Codebase Processing**: Tests with actual files from utils codebase

#### **Server Integration Tests**
- ✅ **Server Health with Semantics**: Verifies server semantic capabilities
- ✅ **Search with Semantic Indicators**: Tests search functionality with semantic results
- ✅ **Codebase Analysis**: Tests semantic-aware codebase analysis
- ✅ **Enhanced Query Processing**: Tests API query enhancement with semantic context

#### **Semantic Metadata Structure**
Each semantically enhanced chunk includes:
```json
{
  "metadata": {
    "semantic_enhanced": true,
    "context_level": "enhanced",
    "semantic_elements": {...},
    "semantic_relationships": [...],
    "semantic_clusters": [...],
    "relevant_semantic_elements": {...}
  }
}
```

#### **Running Semantic Tests**
```bash
# Run all semantic tests
python -m pytest unit-tests -m "semantic" -v

# Run semantic integration tests only
python -m pytest unit-tests/test_semantic_integration.py -v

# Run semantic chunking tests only
python -m pytest unit-tests/test_semantic_chunking_integration.py -v

# Run local semantic tests (skip server integration)
python -m pytest unit-tests/test_semantic_integration.py -v -m "not integration"
```

## 🏆 Benefits

1. **Quality Assurance**: Automated testing ensures server functionality works as expected
2. **Regression Prevention**: Tests catch issues when making changes to the server
3. **Documentation**: Tests serve as living documentation of API behavior
4. **Development Workflow**: Integrated with VS Code for seamless development
5. **CI/CD Ready**: Tests can be integrated into continuous integration pipelines
6. **Semantic Validation**: Comprehensive testing of advanced semantic analysis features
7. **Performance Monitoring**: Built-in performance benchmarking and load testing
8. **Multi-language Coverage**: Validates support for 45+ programming languages

## Next Steps

1. **Start the Server**: Ensure the code analysis server is running on home-ai-server.local:5002
2. **Install Dependencies**: Run `pip install -r requirements.txt`
3. **Run Basic Test**: Execute `python unit-tests/test_basic_connectivity.py`
4. **Full Test Suite**: Run all tests using the provided scripts or VS Code integration

## Troubleshooting

- **Connection Errors**: Verify server is running on home-ai-server.local:5002
- **Import Errors**: Ensure all dependencies are installed
- **Test Failures**: Check server logs and verify codebase availability
- **VS Code Issues**: Reload the window to refresh test discovery

The unit testing framework provides a solid foundation for ensuring the reliability and functionality of the code analysis server.
