{"version": 3, "targets": {".NETCoreApp,Version=v3.0": {"Essenbee.Z80/1.0.1": {"type": "project", "framework": ".NETStandard,Version=v2.0", "compile": {"bin/placeholder/Essenbee.Z80.dll": {}}, "runtime": {"bin/placeholder/Essenbee.Z80.dll": {}}}}}, "libraries": {"Essenbee.Z80/1.0.1": {"type": "project", "path": "../Essenbee.Z80/Essenbee.Z80.csproj", "msbuildProject": "../Essenbee.Z80/Essenbee.Z80.csproj"}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v3.0": ["Essenbee.Z80 >= 1.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80.Debugger\\Essenbee.Z80.Debugger.csproj", "projectName": "Essenbee.Z80.Debugger", "projectPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80.Debugger\\Essenbee.Z80.Debugger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80.Debugger\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.0": {"targetAlias": "netcoreapp3.0", "projectReferences": {"C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj": {"projectPath": "C:\\home-repos\\openwebui_rag_code_server\\source_code\\z80emu\\Essenbee.Z80\\Essenbee.Z80.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.0": {"targetAlias": "netcoreapp3.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[3.0.1, 3.0.1]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[3.0.3, 3.0.3]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[3.0.0, 3.0.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[3.0.0, 3.0.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.315\\RuntimeIdentifierGraph.json"}}}}