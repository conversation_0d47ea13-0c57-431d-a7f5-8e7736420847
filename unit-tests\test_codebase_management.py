"""
Test codebase management endpoints.
"""
import pytest
import httpx


class TestCodebaseManagement:
    """Test codebase management functionality."""

    @pytest.mark.asyncio
    async def test_list_codebases(self, http_client: httpx.AsyncClient, server_health_check):
        """Test listing available codebases."""
        response = await http_client.post("/tools/list_codebases")
        assert response.status_code == 200

        data = response.json()
        # The API returns either "codebases" array or "result" with formatted text
        assert "codebases" in data or "result" in data
        if "result" in data:
            # Count codebases in the formatted result (each has a 🚀 emoji)
            codebase_count = data["result"].count("🚀")
            assert codebase_count > 0  # Should have at least one codebase
        else:
            assert isinstance(data["codebases"], list)

    @pytest.mark.asyncio
    async def test_list_codebases_get_method(self, http_client: httpx.AsyncClient):
        """Test listing codebases using GET method."""
        response = await http_client.get("/tools/list_codebases")
        assert response.status_code == 200

        data = response.json()
        # The API returns either "codebases" array or "result" with formatted text
        assert "codebases" in data or "result" in data

    @pytest.mark.asyncio
    async def test_legacy_codebases_endpoint(self, http_client: httpx.AsyncClient):
        """Test the legacy codebases endpoint."""
        response = await http_client.get("/codebases")
        assert response.status_code == 200
        
        data = response.json()
        assert "codebases" in data or "error" in data

    @pytest.mark.asyncio
    async def test_select_codebase(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test selecting a codebase."""
        payload = {"codebase_name": sample_codebase_name}
        response = await http_client.post("/tools/select_codebase", json=payload)
        
        # Should return 200 regardless of whether codebase exists
        assert response.status_code == 200
        data = response.json()
        assert "result" in data

    @pytest.mark.asyncio
    async def test_get_enhanced_stats(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test getting enhanced statistics for a codebase."""
        payload = {"codebase_name": sample_codebase_name}
        response = await http_client.post("/tools/get_enhanced_stats", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "result" in data

    @pytest.mark.asyncio
    async def test_session_codebase_management(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test session codebase management endpoints."""
        # Set session codebase
        payload = {"codebase_name": sample_codebase_name}
        response = await http_client.post("/tools/set_session_codebase", json=payload)
        assert response.status_code == 200
        
        # Get session codebase
        response = await http_client.get("/tools/get_session_codebase")
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
        
        # Clear session codebase
        response = await http_client.post("/tools/clear_session_codebase")
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
