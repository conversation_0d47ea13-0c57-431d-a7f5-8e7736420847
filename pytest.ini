[pytest]
testpaths = unit-tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    health: marks tests as health check tests
    search: marks tests as search functionality tests
    framework: marks tests as framework integration tests
    codebase: marks tests as codebase management tests
    intent: marks tests as intent detection tests
    analysis: marks tests as analysis endpoints tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
