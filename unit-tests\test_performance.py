"""
Test performance and benchmarking functionality.
Tests response times, throughput, and system performance under load.
"""
import pytest
import httpx
import asyncio
import time
import statistics
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor


class TestPerformance:
    """Test performance and benchmarking capabilities."""

    # Performance test configuration
    BENCHMARK_QUERIES = [
        'memory management functions',
        'error handling patterns', 
        'network operations',
        'file I/O operations',
        'string manipulation',
        'data structures',
        'algorithm implementations'
    ]
    
    PERFORMANCE_THRESHOLDS = {
        'avg_response_time': 5.0,  # seconds (relaxed for real server)
        'p95_response_time': 10.0,  # seconds
        'error_rate': 0.1  # 10%
    }

    @pytest.mark.asyncio
    async def test_basic_response_time(self, http_client: httpx.AsyncClient, server_health_check):
        """Test basic response time for health endpoint."""
        start_time = time.time()
        response = await http_client.get("/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 2.0  # Health endpoint should be fast
        
        print(f"✅ Health endpoint response time: {response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_search_endpoint_performance(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test search endpoint performance with various queries."""
        response_times = []
        errors = 0
        
        for query in self.BENCHMARK_QUERIES[:3]:  # Test first 3 queries
            try:
                payload = {
                    "codebase_name": sample_codebase_name,
                    "query": query,
                    "max_results": 5
                }
                
                start_time = time.time()
                response = await http_client.post("/tools/enhanced_search", json=payload)
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                
                if response.status_code != 200:
                    errors += 1
                    
                print(f"✅ Query '{query}': {response_time:.3f}s")
                
            except httpx.ReadTimeout:
                errors += 1
                print(f"⏰ Query '{query}': Timeout")
            except Exception as e:
                errors += 1
                print(f"❌ Query '{query}': Error - {e}")
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            assert avg_response_time < self.PERFORMANCE_THRESHOLDS['avg_response_time']
            print(f"✅ Average response time: {avg_response_time:.3f}s")
        
        error_rate = errors / len(self.BENCHMARK_QUERIES[:3])
        assert error_rate <= self.PERFORMANCE_THRESHOLDS['error_rate']
        print(f"✅ Error rate: {error_rate:.1%}")

    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test performance under concurrent load."""
        concurrent_requests = 3  # Conservative for real server testing
        query = "memory management functions"
        
        async def make_request():
            try:
                payload = {
                    "codebase_name": sample_codebase_name,
                    "query": query,
                    "max_results": 3
                }
                
                start_time = time.time()
                response = await http_client.post("/tools/enhanced_search", json=payload)
                end_time = time.time()
                
                return {
                    'response_time': end_time - start_time,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                }
            except Exception as e:
                return {
                    'response_time': None,
                    'status_code': None,
                    'success': False,
                    'error': str(e)
                }
        
        # Execute concurrent requests
        start_time = time.time()
        tasks = [make_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        successful_requests = [r for r in results if r['success']]
        response_times = [r['response_time'] for r in successful_requests if r['response_time']]
        
        success_rate = len(successful_requests) / len(results)
        assert success_rate >= 0.8  # At least 80% success rate
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            print(f"✅ Concurrent requests: {concurrent_requests}")
            print(f"✅ Success rate: {success_rate:.1%}")
            print(f"✅ Average response time: {avg_response_time:.3f}s")
            print(f"✅ Total time: {total_time:.3f}s")

    @pytest.mark.asyncio
    async def test_codebase_listing_performance(self, http_client: httpx.AsyncClient):
        """Test codebase listing performance."""
        start_time = time.time()
        response = await http_client.post("/tools/list_codebases")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 3.0  # Should be reasonably fast
        
        print(f"✅ Codebase listing response time: {response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_framework_status_performance(self, http_client: httpx.AsyncClient):
        """Test framework status endpoint performance."""
        start_time = time.time()
        response = await http_client.get("/tools/framework_status")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 2.0  # Should be fast
        
        print(f"✅ Framework status response time: {response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_gpu_status_performance(self, http_client: httpx.AsyncClient):
        """Test GPU status endpoint performance."""
        start_time = time.time()
        response = await http_client.get("/tools/gpu_status")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 2.0  # Should be fast
        
        print(f"✅ GPU status response time: {response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_intent_detection_performance(self, http_client: httpx.AsyncClient):
        """Test intent detection performance."""
        test_queries = [
            "help me understand this code",
            "search for memory functions",
            "what are the main classes?"
        ]
        
        response_times = []
        
        for query in test_queries:
            try:
                payload = {"query": query}
                
                start_time = time.time()
                response = await http_client.post("/tools/detect_intent", json=payload)
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                
                assert response.status_code == 200
                print(f"✅ Intent detection '{query}': {response_time:.3f}s")
                
            except Exception as e:
                print(f"⚠️ Intent detection '{query}': {e}")
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            assert avg_response_time < 1.0  # Intent detection should be fast
            print(f"✅ Average intent detection time: {avg_response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_response_time_consistency(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test response time consistency across multiple requests."""
        query = "memory management"
        payload = {
            "codebase_name": sample_codebase_name,
            "query": query,
            "max_results": 3
        }
        
        response_times = []
        iterations = 3  # Conservative for real server testing
        
        for i in range(iterations):
            try:
                start_time = time.time()
                response = await http_client.post("/tools/enhanced_search", json=payload)
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    print(f"✅ Iteration {i+1}: {response_time:.3f}s")
                
                # Small delay between requests
                await asyncio.sleep(0.5)
                
            except httpx.ReadTimeout:
                print(f"⏰ Iteration {i+1}: Timeout")
            except Exception as e:
                print(f"❌ Iteration {i+1}: {e}")
        
        if len(response_times) >= 2:
            avg_time = statistics.mean(response_times)
            std_dev = statistics.stdev(response_times)
            coefficient_of_variation = std_dev / avg_time
            
            # Response times should be reasonably consistent
            assert coefficient_of_variation < 0.5  # Less than 50% variation
            
            print(f"✅ Average response time: {avg_time:.3f}s")
            print(f"✅ Standard deviation: {std_dev:.3f}s")
            print(f"✅ Coefficient of variation: {coefficient_of_variation:.3f}")

    @pytest.mark.asyncio
    async def test_large_result_set_performance(self, http_client: httpx.AsyncClient, sample_codebase_name):
        """Test performance with larger result sets."""
        payload = {
            "codebase_name": sample_codebase_name,
            "query": "functions",  # Broad query likely to return many results
            "max_results": 20  # Request more results
        }
        
        try:
            start_time = time.time()
            response = await http_client.post("/tools/enhanced_search", json=payload)
            end_time = time.time()
            
            response_time = end_time - start_time
            assert response.status_code == 200
            
            # Large result sets may take longer, but should still be reasonable
            assert response_time < 15.0
            
            print(f"✅ Large result set response time: {response_time:.3f}s")
            
        except httpx.ReadTimeout:
            pytest.skip("Large result set test timed out - this may be expected behavior")
