"""
Test comprehensive language support functionality.
Tests the server's ability to handle all 45+ supported languages.
"""
import pytest
import httpx


class TestLanguageSupport:
    """Test language support and processing capabilities."""

    # Language samples for testing (extracted from debug-testing/test_all_27_languages.py)
    LANGUAGE_SAMPLES = {
        # Core programming languages
        'python': 'def calculate_memory_usage(data): return len(data)',
        'c': 'int calculate_sum(int* array, size_t length) { return sum; }',
        'cpp': 'class Calculator { public: int add(int a, int b); };',
        'csharp': 'public async Task<string> ProcessAsync(string input) { return result; }',
        'javascript': 'function processData(items) { return items.map(x => x * 2); }',
        'typescript': 'interface User { getName(): string; } class UserImpl implements User {}',
        
        # Systems programming
        'rust': 'fn calculate_hash(data: &[u8]) -> u64 { hash_function(data) }',
        'go': 'func ProcessRequest(req *http.Request) (*Response, error) { return nil, nil }',
        'java': 'public class DataProcessor { public void processData(List<String> data) {} }',
        
        # Database and query languages
        'sql': 'CREATE FUNCTION calculate_total(price DECIMAL) RETURNS DECIMAL BEGIN RETURN price * 1.1; END',
        
        # Web development
        'php': 'function getUserData($userId) { return $database->query("SELECT * FROM users"); }',
        'html': '<div class="container"><h1>Title</h1><p>Content</p></div>',
        
        # Scripting languages
        'bash': 'function backup_files() { tar -czf backup.tar.gz /home/<USER>/documents; }',
        'perl': 'sub process_log_file { my ($filename) = @_; open(my $fh, "<", $filename); }',
        'lua': 'function calculate_distance(x1, y1, x2, y2) return math.sqrt((x2-x1)^2 + (y2-y1)^2) end',
        
        # Functional languages
        'lisp': '(defun factorial (n) (if (<= n 1) 1 (* n (factorial (- n 1)))))',
        'scheme': '(define (square x) (* x x)) (define (sum-of-squares x y) (+ (square x) (square y)))',
        
        # Hardware description languages
        'verilog': 'module counter(input clk, input reset, output reg [7:0] count); always @(posedge clk) begin end endmodule',
        'vhdl': 'ENTITY counter IS PORT(clk : IN STD_LOGIC; count : OUT INTEGER); END ENTITY;',
        
        # Scientific computing
        'fortran': 'SUBROUTINE CALCULATE_MATRIX(A, B, C, N) INTEGER N REAL A(N,N), B(N,N), C(N,N) END SUBROUTINE',
        'matlab': 'function result = calculateSum(matrix) result = sum(matrix(:)); end',
        
        # Data formats
        'json': '{"name": "test", "functions": ["getData", "processData"], "version": "1.0"}',
        'yaml': 'functions:\n  - name: getData\n    type: async\n  - name: processData\n    type: sync',
        'xml': '<functions><function name="getData" type="async"/><function name="processData"/></functions>',
        'toml': '[functions]\ngetData = "async"\nprocessData = "sync"',
        
        # Documentation
        'markdown': '# Functions\n## getData()\nRetrieves data from API\n## processData()\nProcesses the data',
        
        # Additional languages
        'scala': 'def processData(items: List[Int]): List[Int] = items.map(_ * 2)',
        'kotlin': 'fun processData(items: List<Int>): List<Int> = items.map { it * 2 }',
        'swift': 'func processData(items: [Int]) -> [Int] { return items.map { $0 * 2 } }',
        'ruby': 'def process_data(items); items.map { |x| x * 2 }; end',
        'r': 'processData <- function(data) { return(data * 2) }',
        'erlang': 'process_data(Items) -> lists:map(fun(X) -> X * 2 end, Items).',
        'elixir': 'def process_data(items), do: Enum.map(items, &(&1 * 2))',
        'haskell': 'processData :: [Int] -> [Int]; processData = map (*2)',
        'clojure': '(defn process-data [items] (map #(* % 2) items))',
        'groovy': 'def processData(items) { items.collect { it * 2 } }',
        'pascal': 'function ProcessData(items: array of Integer): array of Integer; begin end;',
        'assembly': 'section .text\nglobal _start\n_start:\n    mov eax, 1\n    int 0x80',
        'cmake': 'function(process_data items result)\n    set(${result} ${items} PARENT_SCOPE)\nendfunction()',
        'makefile': 'process_data:\n\t@echo "Processing data"\n\t@gcc -o output input.c',
        'css': '.container { display: flex; } .item { margin: 10px; }',
        'scss': '$primary-color: #333; .container { color: $primary-color; }',
        'tex': '\\documentclass{article} \\begin{document} \\section{Functions} \\end{document}',
        'prolog': 'process_data([], []). process_data([H|T], [H2|T2]) :- H2 is H * 2, process_data(T, T2).',
        'metta': '(= (process-data $items) (map (* 2) $items))'
    }

    @pytest.mark.asyncio
    async def test_supported_languages_endpoint(self, http_client: httpx.AsyncClient, server_health_check):
        """Test that the server reports comprehensive language support."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "supported_languages" in data
        
        supported_languages = data["supported_languages"]
        assert isinstance(supported_languages, list)
        assert len(supported_languages) >= 40  # Should support 40+ languages
        
        # Check for key languages
        expected_languages = ['python', 'javascript', 'typescript', 'c_cpp', 'java', 'rust', 'go']
        for lang in expected_languages:
            assert lang in supported_languages, f"Language {lang} should be supported"

    @pytest.mark.asyncio
    async def test_language_detection_capability(self, http_client: httpx.AsyncClient):
        """Test the server's language detection capabilities."""
        # Test with a few key language samples
        test_samples = {
            'python': self.LANGUAGE_SAMPLES['python'],
            'javascript': self.LANGUAGE_SAMPLES['javascript'],
            'rust': self.LANGUAGE_SAMPLES['rust'],
            'go': self.LANGUAGE_SAMPLES['go']
        }
        
        for expected_lang, code_sample in test_samples.items():
            # This would test language detection if such an endpoint exists
            # For now, we'll test that the server can handle different language queries
            payload = {
                "query": f"analyze this {expected_lang} code: {code_sample}",
                "codebase_name": "utils"
            }
            
            try:
                response = await http_client.post("/tools/enhanced_search", json=payload)
                assert response.status_code == 200
                # The server should be able to process language-specific queries
                data = response.json()
                assert "result" in data or "error" in data
            except httpx.ReadTimeout:
                # Language processing might timeout, which is acceptable
                pytest.skip(f"Language detection test for {expected_lang} timed out")

    @pytest.mark.asyncio
    async def test_framework_language_coverage(self, http_client: httpx.AsyncClient):
        """Test that the framework reports comprehensive language coverage."""
        response = await http_client.get("/tools/framework_status")
        assert response.status_code == 200
        
        data = response.json()
        assert "supported_languages" in data
        assert "supported_extensions" in data
        
        # Should support 45+ languages and 75+ extensions
        assert data["supported_languages"] >= 40
        assert data["supported_extensions"] >= 70

    @pytest.mark.parametrize("language,code_sample", [
        ("python", "def test(): pass"),
        ("javascript", "function test() {}"),
        ("rust", "fn test() {}"),
        ("go", "func test() {}"),
        ("java", "public void test() {}"),
        ("cpp", "void test() {}"),
    ])
    @pytest.mark.asyncio
    async def test_language_specific_queries(self, http_client: httpx.AsyncClient, language, code_sample):
        """Test language-specific code analysis queries."""
        payload = {
            "query": f"find functions in this {language} code",
            "codebase_name": "utils"
        }
        
        try:
            response = await http_client.post("/tools/enhanced_search", json=payload)
            assert response.status_code == 200
            
            data = response.json()
            assert "result" in data or "error" in data
        except httpx.ReadTimeout:
            pytest.skip(f"Language-specific query for {language} timed out")

    @pytest.mark.asyncio
    async def test_multi_language_codebase_support(self, http_client: httpx.AsyncClient):
        """Test that the server can handle multi-language codebases."""
        # Get list of codebases
        response = await http_client.post("/tools/list_codebases")
        assert response.status_code == 200
        
        data = response.json()
        if "result" in data:
            # Check that codebases show multiple languages
            result_text = data["result"]
            assert "Languages:" in result_text
            # Should show multiple languages for at least some codebases
            assert "C++" in result_text or "JavaScript" in result_text or "Python" in result_text

    @pytest.mark.asyncio
    async def test_language_extension_mapping(self, http_client: httpx.AsyncClient):
        """Test that the server properly maps file extensions to languages."""
        response = await http_client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        if "current_codebase_info" in data:
            codebase_info = data["current_codebase_info"]
            if "file_counts" in codebase_info and "detected_languages" in codebase_info:
                # Should properly map extensions to languages
                file_counts = codebase_info["file_counts"]
                detected_languages = codebase_info["detected_languages"]
                
                # If there are .py files, Python should be detected
                if ".py" in file_counts:
                    assert "Python" in detected_languages
                
                # If there are .js files, JavaScript should be detected
                if ".js" in file_counts:
                    assert "JavaScript" in detected_languages
