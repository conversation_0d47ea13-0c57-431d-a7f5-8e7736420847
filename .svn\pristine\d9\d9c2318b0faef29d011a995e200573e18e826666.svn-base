# Unit Tests for OpenWebUI RAG Code Server

This directory contains comprehensive unit tests for the code analysis server running on port 5002.

## Test Structure

The tests are organized into the following modules:

- **`test_server_health.py`** - Tests server health endpoints and basic connectivity
- **`test_codebase_management.py`** - Tests codebase management functionality
- **`test_search_functionality.py`** - Tests search and query endpoints
- **`test_framework_integration.py`** - Tests new framework integration functionality
- **`test_intent_detection.py`** - Tests intent detection service endpoints
- **`test_analysis_endpoints.py`** - Tests analysis service functionality

## Prerequisites

1. **Server Running**: The code analysis server must be running on home-ai-server.local:5002
2. **Dependencies**: Install test dependencies with:
   ```bash
   pip install -r requirements.txt
   ```

## Running Tests

### From Visual Studio Code

1. **Test Explorer**: Open the Test Explorer panel in VS Code and run tests interactively
2. **Debug Configuration**: Use the "Run Unit Tests" debug configuration from the Run and Debug panel
3. **Command Palette**: Use `Python: Run All Tests` or `Python: Run Current Test File`

### From Command Line

#### Run All Tests
```bash
python unit-tests/run_tests.py --verbose
```

#### Run Specific Test File
```bash
python unit-tests/run_tests.py --pattern test_server_health.py --verbose
```

#### Run Tests with Specific Markers
```bash
python unit-tests/run_tests.py --markers "health" --verbose
```

#### Run with Coverage
```bash
python unit-tests/run_tests.py --coverage --verbose
```

### Using pytest Directly
```bash
# Run all tests
python -m pytest unit-tests -v

# Run specific test file
python -m pytest unit-tests/test_server_health.py -v

# Run with markers
python -m pytest unit-tests -m "health" -v

# Run with coverage
python -m pytest unit-tests --cov=. --cov-report=html -v
```

## Test Configuration

### Server Configuration
- **Base URL**: `http://home-ai-server.local:5002`
- **Timeout**: 30 seconds
- **Sample Codebase**: `utils` (based on available codebases)

### Test Markers
- `health` - Health check tests
- `search` - Search functionality tests
- `framework` - Framework integration tests
- `codebase` - Codebase management tests
- `intent` - Intent detection tests
- `analysis` - Analysis endpoints tests

## Test Features

### Health Checks
- Server connectivity verification
- Health endpoint validation
- Status endpoint testing
- Enhanced features documentation

### Codebase Management
- List available codebases
- Select/unselect codebases
- Session management
- Statistics retrieval

### Search Functionality
- Enhanced search testing
- Optimized context retrieval
- Legacy endpoint compatibility
- Stateless search operations

### Framework Integration
- GPU infrastructure testing
- Framework status validation
- Query enhancement testing
- Pattern analysis

### Intent Detection
- Query intent classification
- Configuration management
- Pattern matching validation

## Troubleshooting

### Server Not Available
If tests fail with connection errors:
1. Verify the server is running on home-ai-server.local:5002
2. Check server health at `http://home-ai-server.local:5002/health`
3. Ensure no firewall blocking the connection

### Test Failures
- Check server logs for errors
- Verify codebase availability
- Ensure all dependencies are installed
- Run individual test files to isolate issues

### Debug Mode
Use the "Debug Unit Test" configuration in VS Code to step through test execution and inspect variables.

## Adding New Tests

1. Create new test files following the `test_*.py` naming convention
2. Use the existing fixtures from `conftest.py`
3. Add appropriate test markers
4. Follow the existing test patterns for consistency

## Continuous Integration

These tests are designed to be run in CI/CD pipelines. The test runner script provides appropriate exit codes for automation.
