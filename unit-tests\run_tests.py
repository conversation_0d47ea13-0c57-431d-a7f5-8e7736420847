#!/usr/bin/env python3
"""
Test runner script for the code analysis server unit tests.
This script can be executed from Visual Studio Code or command line.
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_pattern=None, verbose=False, coverage=False, markers=None):
    """Run the unit tests with specified options."""
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test path
    cmd.append("unit-tests")
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add specific test pattern if provided
    if test_pattern:
        cmd.append(f"unit-tests/{test_pattern}")
    
    # Add marker filtering if provided
    if markers:
        cmd.extend(["-m", markers])
    
    # Add coverage if requested
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])
    
    # Add other useful options
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description="Run unit tests for the code analysis server")
    
    parser.add_argument(
        "--pattern", "-p",
        help="Test file pattern (e.g., 'test_health.py' or 'test_*health*')"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Run tests with coverage reporting"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="Run tests with specific markers (e.g., 'health', 'not slow')"
    )
    
    parser.add_argument(
        "--list-tests", "-l",
        action="store_true",
        help="List available test files"
    )
    
    args = parser.parse_args()
    
    if args.list_tests:
        print("Available test files:")
        test_dir = Path("unit-tests")
        for test_file in sorted(test_dir.glob("test_*.py")):
            print(f"  - {test_file.name}")
        return 0
    
    return run_tests(
        test_pattern=args.pattern,
        verbose=args.verbose,
        coverage=args.coverage,
        markers=args.markers
    )


if __name__ == "__main__":
    sys.exit(main())
