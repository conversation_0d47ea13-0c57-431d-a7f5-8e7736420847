{".class": "MypyFile", "_fullname": "cleanup_redundant_tests", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TestingCleanup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cleanup_redundant_tests.TestingCleanup", "name": "TestingCleanup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cleanup_redundant_tests", "mro": ["cleanup_redundant_tests.TestingCleanup", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "dry_run"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "dry_run"], "arg_types": ["cleanup_redundant_tests.TestingCleanup", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TestingCleanup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dry_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cleanup_redundant_tests.TestingCleanup.dry_run", "name": "dry_run", "type": "builtins.bool"}}, "get_files_to_remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup.get_files_to_remove", "name": "get_files_to_remove", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cleanup_redundant_tests.TestingCleanup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_files_to_remove of TestingCleanup", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kept_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cleanup_redundant_tests.TestingCleanup.kept_files", "name": "kept_files", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "remove_file_or_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup.remove_file_or_dir", "name": "remove_file_or_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["cleanup_redundant_tests.TestingCleanup", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_file_or_dir of TestingCleanup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "removed_dirs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cleanup_redundant_tests.TestingCleanup.removed_dirs", "name": "removed_dirs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "removed_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cleanup_redundant_tests.TestingCleanup.removed_files", "name": "removed_files", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "run_cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup.run_cleanup", "name": "run_cleanup", "type": null}}, "should_keep_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.TestingCleanup.should_keep_file", "name": "should_keep_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["cleanup_redundant_tests.TestingCleanup", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_keep_file of TestingCleanup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cleanup_redundant_tests.TestingCleanup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cleanup_redundant_tests.TestingCleanup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cleanup_redundant_tests.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cleanup_redundant_tests.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\cleanup_redundant_tests.py"}