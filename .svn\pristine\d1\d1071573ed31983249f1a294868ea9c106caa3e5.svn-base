# 🧠 Semantic Analysis User Guide

> 📖 **Related Documentation**:
> - [SEMANTIC_ENHANCEMENT_IMPLEMENTATION.md](SEMANTIC_ENHANCEMENT_IMPLEMENTATION.md) - Technical implementation details
> - [SEMANTIC_TESTING_GUIDE.md](SEMANTIC_TESTING_GUIDE.md) - Testing procedures
> - [USER_GUIDE.md](USER_GUIDE.md) - General user guide

## 🚀 **New Capabilities**

### **1. Enhanced Search Results**
The system now provides much richer context for code queries:

**Before:**
- Query: `"explain tmwmem_lowFree"`
- Results: 5 individual function chunks without context

**After:**
- Query: `"explain tmwmem_lowFree"`
- Results: Semantic clusters with:
  - Function documentation
  - Related data structures (TMWMEM_POOL_STRUCT, TMWMEM_HEADER)
  - Configuration macros (TMWCNFG_USE_DYNAMIC_MEMORY)
  - Usage examples (tmwmem_free calling tmwmem_lowFree)
  - Thread safety context (TMWTARG_LOCK_SECTION)

### **2. New API Endpoint**
```bash
POST /api/v1/semantic_search
{
  "query": "explain tmwmem_lowFree function",
  "codebase_name": "utils",
  "n_results": 10,
  "context_level": 4
}
```

**Response includes:**
- Enhanced search results with semantic ranking
- Multi-level context (signature, structures, usage, configuration)
- Query enhancement details
- Semantic analysis metadata

### **3. Multi-Level Context**
Context levels provide hierarchical information:
- **Level 1**: Function signature + documentation
- **Level 2**: Data structures and macros used
- **Level 3**: Usage patterns and examples
- **Level 4**: Configuration options that affect behavior

### **4. Automatic Enhancement**
The system automatically:
- Groups related functions (alloc/free pairs)
- Includes data structures with functions that use them
- Preserves documentation comments
- Links configuration macros to affected code
- Builds dependency relationships

## 🎯 **Usage Examples**

### **Function Explanation Queries**
```bash
# These queries now return comprehensive context:
"explain tmwmem_lowFree function"
"describe tmwmem_alloc"
"what does TMWMEM_POOL_STRUCT do"
```

### **Usage Pattern Queries**
```bash
# These find usage examples and calling patterns:
"how to use tmwmem_alloc"
"usage examples of tmwmem_free"
"calling patterns for memory functions"
```

### **Configuration Queries**
```bash
# These show configuration dependencies:
"configuration for memory management"
"TMWCNFG_USE_DYNAMIC_MEMORY settings"
"memory allocation options"
```

## 📊 **Performance**
- Processing: ~833 chunks/second
- Query enhancement: <1ms average
- Semantic analysis: ~1670 elements/second
- Memory usage: Minimal overhead

## 🔧 **Integration Status**
✅ TreeSitterChunker enhanced with semantic capabilities
✅ Main service integrated with semantic analyzer
✅ New semantic search endpoint added
✅ Multi-level context retrieval available
✅ Automatic query enhancement active

The semantic enhancements are now active and will automatically improve search results for C/C++ codebases!
